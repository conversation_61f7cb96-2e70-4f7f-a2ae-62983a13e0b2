<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Migrations\**" />
    <EmbeddedResource Remove="Migrations\**" />
    <None Remove="Migrations\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Bench.cs" />
    <Compile Remove="Btd6.cs" />
    <Compile Remove="GodotParser.cs" />
    <Compile Remove="Hsdbf.cs" />
    <Compile Remove="hshash.cs" />
    <Compile Remove="MonitorBackpackBattles.cs" />
    <Compile Remove="Program.cs" />
    <Compile Remove="Requester.cs" />
    <Compile Remove="TestUnity.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="DBDMarket1.base64" />
    <None Remove="DBDMarket2.base64" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet" Version="0.13.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Facepunch.Steamworks.Win64">
      <HintPath>..\..\Facepunch.Steamworks\Artifacts\Facepunch.Steamworks.Win64.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
