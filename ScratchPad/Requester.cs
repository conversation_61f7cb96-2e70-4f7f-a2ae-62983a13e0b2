﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace ScratchPad
{
    public class Requester
    {
        static WebProxy Proxy;
        internal CookieContainer CookieContainer { get; private set; }
        internal HttpClient Client { get; private set; }
        internal string UserAgent
        {
            get => this._ua;
            set
            {
                this._ua = value;
                this.Client.DefaultRequestHeaders.Add(HttpRequestHeader.UserAgent.ToString(), this._ua);
            }
        }

        private string _ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:100.0) Gecko/20100101 Firefox/100.0";

        internal Requester()
        {
            this.CookieContainer = new CookieContainer();
            this.Client = new HttpClient(new HttpClientHandler()
            {
                Proxy = Proxy,
                CookieContainer = this.CookieContainer
            });
            this.Client.DefaultRequestHeaders.Add("User-Agent", this.UserAgent);
            this.Client.DefaultRequestHeaders.Add("Accept-Language", "en-US");
            this.Client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            this.Client.DefaultRequestHeaders.Add("DNT", "1");
            this.Client.DefaultRequestHeaders.Add("Connection", "keep-alive");
            this.Client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
        }

        ~Requester()
        {
            this.Client.Dispose();
        }

        public static void Init(string host, int port)
        {
            Proxy = (!string.IsNullOrWhiteSpace(host) && port > 0) ? new WebProxy(host, port) : null;
        }

        public static string Get(string url)
        {
            return new Requester().Client.GetStringAsync(url).Result;
        }
    }
}
