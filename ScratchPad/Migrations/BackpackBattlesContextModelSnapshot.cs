﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace ScratchPad.Migrations
{
    [DbContext(typeof(BackpackBattlesParser.BackpackBattlesContext))]
    partial class BackpackBattlesContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "character_class", new[] { "ranger", "reaper", "berserker", "pyromancer" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "item_id", new[] { "stone", "wooden_sword", "pan", "broom", "wooden_buckler", "banana", "garlic", "healing_herbs", "walrus_tusk", "piggybank", "whetstone", "pocket_sand", "lump_of_coal", "leather_bag", "hammer", "spear", "axe", "dagger", "spiked_shield", "leather_armor", "gloves_of_haste", "health_potion", "fly_agaric", "blueberries", "goobert", "bag_of_stones", "lucky_clover", "customer_card", "fanny_pack", "carrot", "coins", "shovel", "deck_of_cards", "burning_coal", "torch", "the_lovers", "ace_of_spades", "white_eyes_blue_dragon", "the_fool", "holo_fire_lizard", "darkest_lotus", "thorn_whip", "hungry_blade", "bow_and_arrow", "leather_boots", "leather_helm", "pestilence_flask", "stone_skin_potion", "acorn_collar", "flute", "mana_orb", "corrupted_crystal", "stamina_sack", "hero_sword", "magic_staff", "strong_health_potion", "poison_dagger", "poison_goobert", "claws_of_attack", "lucky_piggy", "carrot_goobert", "burning_torch", "platin_customer_card", "mana_potion", "rib_saw_blade", "death_scythe", "holy_armor", "shield_of_valor", "heroic_potion", "pineapple", "blood_amulet", "ruby_egg", "jynx_torquilla", "potion_belt", "bloody_dagger", "strong_heroic_potion", "strong_stone_skin_potion", "critwood_staff", "vampiric_armor", "blood_goobert", "steel_goobert", "eggscalibur", "manathirst", "spectral_dagger", "hero_longsword", "falcon_blade", "pandamonium", "thorn_bow", "lucky_bow", "poison_bow", "evil_cap", "magic_torch", "fanfare", "lightsaber", "book_of_light", "demonic_flask", "protective_purse", "fancy_fencing_rapier", "djinn_lamp", "greatsword", "crown", "wolpertinger", "heart_container", "bloodthorne", "crossblades", "staff_of_unhealing", "ruby_whelp", "vampiric_gloves", "darksaber", "light_goobert", "rainbow_goobert", "rainbow_goobert_ranger", "ruby_chonk", "holy_spear", "moon_shield", "corrupted_armor", "ranger_bag", "storage_coffin", "artifact_stone_cold", "vampiric_scythe", "villain_sword", "skull", "artifact_stone_heat", "present", "gingerbread_man", "pumpkin", "dancing_dragon", "piercing_arrow", "yggdrasil_leaf", "poison_ivy", "mega_clover", "bowl_of_treats", "cursed_hair_comb", "mr_struggles", "cursed_dagger", "box_of_riches", "chipped_ruby", "flawed_ruby", "regular_ruby", "flawless_ruby", "perfect_ruby", "chipped_sapphire", "flawed_sapphire", "regular_sapphire", "flawless_sapphire", "perfect_sapphire", "chipped_emerald", "flawed_emerald", "regular_emerald", "flawless_emerald", "perfect_emerald", "chipped_topaz", "flawed_topaz", "regular_topaz", "flawless_topaz", "perfect_topaz", "chipped_amethyst", "flawed_amethyst", "regular_amethyst", "flawless_amethyst", "perfect_amethyst", "snake", "cauldron", "shortbow", "thorn_shortbow", "lucky_shortbow", "poison_shortbow", "rat", "squirrel", "hedgehog", "strong_pestilence_flask", "strong_demonic_flask", "strong_mana_potion", "vampiric_potion", "strong_vampiric_potion", "divine_potion", "strong_divine_potion", "fire_pit", "flame", "phoenix", "burning_sword", "burning_blade", "chili_pepper", "draconic_orb", "staff_of_fire", "flame_whip", "obsidian_dragon", "friendly_fire", "burning_banner", "dark_lantern", "frozen_flame", "dragon_nest", "berserker_bag", "forging_hammer", "spiked_collar", "double_axe", "chain_whip", "dragonscale_armor", "dragon_claws", "anvil", "deer_totem", "wolf_emblem", "shaman_mask", "brass_knuckles", "courage_puppy", "wisdom_puppy", "power_puppy", "busted_blade", "dragonskin_boots", "spell_scroll_frostbolt", "book_of_ice", "ice_armor", "frozen_buckler", "frostbite", "badger_rune", "elephant_rune", "hawk_rune", "tiger_rune", "emerald_egg", "emerald_whelp", "sapphire_egg", "sapphire_whelp", "amethyst_egg", "amethyst_whelp", "cheese", "cheese_goobert", "chili_goobert", "molten_dagger", "molten_spear", "rainbow_goobert_pyromancer", "rainbow_goobert_berserker", "armored_courage_puppy", "armored_wisdom_puppy", "armored_power_puppy", "sun_armor", "sun_shield", "moon_armor", "ice_dragon", "stone_armor", "stone_helm", "stone_gloves", "reverse", "mrs_struggles", "miss_fortune", "heart_of_darkness", "joker", "leaf_badge", "skull_badge", "wolf_badge", "flame_badge", "rainbow_badge", "stone_badge", "vineweave_basket", "relic_case", "portable_altar", "toolbox" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "league", new[] { "cheater", "gramdma", "grand_master", "master", "diamond", "platinum", "gold", "silver", "bronze", "unranked" });
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("BackpackBattlesParser+BackpackBattlesContext+FailedRun", b =>
                {
                    b.Property<decimal>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(20,0)");

                    b.Property<int>("Score")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Failed");
                });

            modelBuilder.Entity("BackpackBattlesParser+GameRun", b =>
                {
                    b.Property<decimal>("UgcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(20,0)");

                    b.Property<BackpackBattlesParser.Definitions.CharacterClass>("Character")
                        .HasColumnType("character_class");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<BackpackBattlesParser.Definitions.League>("League")
                        .HasColumnType("league");

                    b.Property<int>("MatchMakingScore")
                        .HasColumnType("integer");

                    b.Property<string>("PlayerName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double>("Rating")
                        .HasColumnType("double precision");

                    b.Property<decimal>("SteamId")
                        .HasColumnType("numeric(20,0)");

                    b.Property<string>("UniqueId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("UgcId");

                    b.ToTable("Games");
                });

            modelBuilder.Entity("BackpackBattlesParser+GameRun", b =>
                {
                    b.OwnsMany("BackpackBattlesParser+Definitions+Turn", "Rounds", b1 =>
                        {
                            b1.Property<decimal>("GameRunUgcId")
                                .HasColumnType("numeric(20,0)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<short>("Health")
                                .HasColumnType("smallint");

                            b1.Property<short>("Stamina")
                                .HasColumnType("smallint");

                            b1.Property<bool>("Win")
                                .HasColumnType("boolean");

                            b1.HasKey("GameRunUgcId", "Id");

                            b1.ToTable("Games");

                            b1.ToJson("Rounds");

                            b1.WithOwner()
                                .HasForeignKey("GameRunUgcId");

                            b1.OwnsMany("BackpackBattlesParser+Definitions+Item", "Items", b2 =>
                                {
                                    b2.Property<decimal>("TurnGameRunUgcId")
                                        .HasColumnType("numeric(20,0)");

                                    b2.Property<int>("TurnId")
                                        .HasColumnType("integer");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer");

                                    b2.Property<byte>("Direction")
                                        .HasColumnType("smallint");

                                    b2.Property<int[]>("GemSockets")
                                        .HasColumnType("integer[]");

                                    b2.Property<int>("Type")
                                        .HasColumnType("integer");

                                    b2.Property<byte>("X")
                                        .HasColumnType("smallint");

                                    b2.Property<byte>("Y")
                                        .HasColumnType("smallint");

                                    b2.HasKey("TurnGameRunUgcId", "TurnId", "Id");

                                    b2.ToTable("Games");

                                    b2.WithOwner()
                                        .HasForeignKey("TurnGameRunUgcId", "TurnId");
                                });

                            b1.Navigation("Items");
                        });

                    b.Navigation("Rounds");
                });
#pragma warning restore 612, 618
        }
    }
}
