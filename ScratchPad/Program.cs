﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace ScratchPad
{

    internal class Program
    {
        private static void Main(string[] args)
        {
            Mixture();
        }

        public class BilibiliUser { public int Uid; public int Ruid; public int Rank; public string Username; public string Face; public int IsAlive; public int GuardLevel; public int GuardSubLevel; public MedalInfo MedalInfo; }
        public class MedalInfo { public string MedalName; public int MedalLevel; public int MedalColorStart; public int MedalColorEnd; public int MedalColorBorder; }

        private static void Mixture()
        {
            Console.WriteLine(DateTime.Now.Ticks);
            var todos = new ConcurrentQueue<int>(Enumerable.Range(0, 350));
            var retry = new ConcurrentDictionary<int, int>();
            var items = new ConcurrentBag<BilibiliUser>();
            var settings = new JsonSerializerSettings { ContractResolver = new DefaultContractResolver { NamingStrategy = new SnakeCaseNamingStrategy() } };
            var output = "";
            void Worker()
            {
                while (true)
                {
                    if (!todos.TryDequeue(out var i))
                    {
                        return;
                    }
                    try
                    {
                        lock (output)
                        {
                            Console.WriteLine($"{i} working");
                        }
                        var resp = Requester.Get($"https://api.live.bilibili.com/xlive/app-room/v2/guardTab/topList?roomid=22625025&page={i}&ruid=672346917&page_size=25");
                        var jo = JsonConvert.DeserializeObject<JObject>(resp);
                        foreach (var itemjo in (JArray)jo["data"]["list"])
                        {
                            items.Add(JsonConvert.DeserializeObject<BilibiliUser>(itemjo.ToString(), settings));
                        }
                        lock (output)
                        {
                            Console.WriteLine($"{i} done");
                        }
                    }
                    catch (Exception e)
                    {
                        lock (output)
                        {
                            Console.WriteLine();
                            Console.WriteLine($"{i} not working: {e}");
                            retry.AddOrUpdate(i, 1, (_, cur) => cur + 1);
                            if (retry.TryGetValue(i, out var count) && count < 5)
                            {
                                todos.Enqueue(i);
                            }
                        }
                    }
                    try
                    {
                        File.WriteAllText("bilibili_672346917_guardTab", JsonConvert.SerializeObject(items, Formatting.Indented));
                    }
                    catch
                    {
                    }
                }
            }

            Requester.Init("127.0.0.1", 10803);

            Task.WaitAll(Enumerable.Range(0, 4)
                .Select(_ => Task.Factory.StartNew(Worker))
                .ToArray());

            File.WriteAllText("bilibili_672346917_guardTab", JsonConvert.SerializeObject(items, Formatting.Indented));
            Console.WriteLine(DateTime.Now.Ticks);
        }

        private static void Encode()
        {
            // 给定长度100*1024个字符串，其中每个100的字符串前96字节为内容，剩下4字节为冗余。
            // 在冗余中藏20字节的内容，并在随机取出字符串后从取出的字符串里尽量恢复20字节的内容
            // 研究：
            // 在取出字符串数量减小时的恢复效果
            // 冗余从4变为2后的效果

            var bm = new Bitmap(500, 500);
            using var grp = Graphics.FromImage(bm);
            var totalPcs = 1024;
            var counter = 0;
            var totalData = 160;
            var types = new Dictionary<int, Pen>
            {
                [8 * 2] = new Pen(new SolidBrush(Color.FromArgb(127, Color.Red)), 2),
                [6 * 2] = new Pen(new SolidBrush(Color.FromArgb(127, Color.Yellow)), 2),
                [8 * 4] = new Pen(new SolidBrush(Color.FromArgb(127, Color.Blue)), 2),
                [6 * 4] = new Pen(new SolidBrush(Color.FromArgb(127, Color.Green)), 2),
            };
            while (counter++ <= 100)
            {
                foreach (var item in types)
                {
                    for (int sample = 0; sample < totalPcs; sample++)
                    {
                        var works = 0;
                        for (int times = 0; times < 100; times++)
                        {
                            var result = new bool[totalData];
                            // 想了想，似乎没什么好方法可以高性能sample，坑了。换rust试试去
                        }
                    }
                }
            }

            bm.Save("test.bmp");
        }

        private static byte[] ReadResource(string name)
        {
            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = assembly.GetManifestResourceNames().First(i => i.EndsWith(name));

            using (var stream = assembly.GetManifestResourceStream(resourceName))
            using (var reader = new BinaryReader(stream))
            {
                var ba = new byte[stream.Length];
                stream.Read(ba, 0, ba.Length);
                return ba;
            }
        }

        private static void Unzip()
        {
            static byte[] Decompress(byte[] input)
            {
                byte[] result;
                using (var memoryStream = new MemoryStream(input))
                {
                    var array = new byte[4];
                    memoryStream.Read(array, 0, 4);
                    var num = BitConverter.ToInt32(array, 0);
                    using (var gzipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
                    {
                        var array2 = new byte[num];
                        gzipStream.Read(array2, 0, num);
                        result = array2;
                    }
                }
                return result;
            }

            var r = ReadResource("DBDMarket2.base64");
            var r2 = Encoding.ASCII.GetString(r);
            var r3 = Convert.FromBase64String(r2);
            var r4 = Decompress(r3);
            Console.WriteLine(Encoding.UTF8.GetString(r4));
        }

        private static void DrawGrad()
        {
            const int SIZE = 500;
            const int PADDING = 50;
            const int SIMCOUNT = 10000;
            int[] Simulate()
            {
                var rng = new Random();
                var result = new int[SIMCOUNT]; for (var i = 0; i < SIMCOUNT; i++)
                {
                    switch (rng.Next(8))
                    {
                        case 0:
                        case 1:
                            result[i] = rng.Next(10, 20); break;
                        case 2:
                            result[i] = rng.Next(5, 10); break;
                        case 3:
                        case 4:
                        case 5:
                            result[i] = 0; break;
                        case 6:
                            result[i] = -rng.Next(5, 10); break;
                        case 7:
                            result[i] = -rng.Next(10, 40); break;
                    }
                }
                return result;
            }
            (byte R, byte G, byte B) FromHV(double h, double v)
            {
                // H: [0, 2], S: 1, V: [0, 1]
                h = 2 - h;
                const int s = 1;
                var c = s * v;
                var x = h % 2 > 1 ? 2 - (h % 2) : h % 2;
                var m = v - c;
                var (r, g, b) = h switch
                {
                    double when h >= 0 && h < 1 => (c, x, 0),
                    double when h >= 1 && h < 2 => (x, c, 0),
                    double when h >= 2 && h < 3 => (0, c, x),
                    double when h >= 3 && h < 4 => (0, x, c),
                    double when h >= 4 && h < 5 => (x, 0, c),
                    _ => (c, 0.0, x),
                };
                return ((byte)((r + m) * 255), (byte)((g + m) * 255), (byte)((b + m) * 255));
            }
            var bm = new Bitmap(SIZE + 2 * PADDING, SIZE + 2 * PADDING);
            using var grp = Graphics.FromImage(bm);
            var rect = new Rectangle(PADDING, PADDING, SIZE, SIZE);
            var path = new GraphicsPath();
            path.AddEllipse(rect);
            path.Flatten();
            var result = Simulate().OrderBy(x => x).ToArray();
            var maxDmg = result.Where(x => x > 0).Max();
            var lthDmg = 10;
            var minDmg = result.Where(x => x > 0).Min();
            var maxHrt = result.Where(x => x < 0).Min();
            var lthHrt = -10;
            var minHrt = result.Where(x => x < 0).Max();
            var completed = 0;
            var estPoints = result.Length;
            var totalPoints = path.PointCount;
            const double value = 0.7;
            var surround_colors = new Color[totalPoints];
            for (var i = 0; i < estPoints; i++)
            {
                var p = result[i];
                var (R, G, B) = p switch
                {
                    int when p >= maxHrt && p < lthHrt => FromHV(0, (p - maxHrt) * value / (lthHrt - maxHrt) + 1 - value),
                    int when p >= lthHrt && p <= minHrt => FromHV((p - lthHrt) * 1.0 / -lthHrt, 1),
                    int when p == 0 => FromHV(1, 1),
                    int when p >= minDmg && p < lthDmg => FromHV(1 + p * 1.0 / lthDmg, 1),
                    int when p >= lthDmg && p <= maxDmg => FromHV(2, (maxDmg - p) * value / (maxDmg - lthDmg) + 1 - value),
                    _ => throw new NotImplementedException()
                };
                for (; completed < i * 1.0 / estPoints * totalPoints; completed++)
                {
                    surround_colors[completed] = Color.FromArgb(255, R, G, B);
                }
            }
            surround_colors = surround_colors.Skip(totalPoints / 4).Concat(surround_colors.Take(totalPoints / 4)).ToArray();
            using (var path_brush = new PathGradientBrush(path))
            {
                path_brush.CenterColor = Color.White;
                path_brush.SurroundColors = surround_colors;
                grp.FillPath(path_brush, path);
            }
            bm.Save("test.bmp");
        }

        private static void Dedupe()
        {
            const string SRC = @"C:\WindowsSandbox\Download";
            const string TGT = @"C:\Legacy\USB2\Histo";
            foreach (var file in Directory.GetFiles(SRC, "*.*", SearchOption.AllDirectories))
            {
                var nf = file.Replace(SRC, TGT).Replace("(1)", "");
                if (file.Contains(".git"))
                {
                    continue;
                }
                if (!Directory.Exists(Path.GetDirectoryName(nf)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(nf));
                }
                if (!File.Exists(nf))
                {
                    File.Move(file, nf);
                    Console.WriteLine($"Mv {file} {nf}");
                }
                else
                {
                    if (new FileInfo(file).Length == new FileInfo(nf).Length)
                    {
                        if (File.ReadAllBytes(file).SequenceEqual(File.ReadAllBytes(nf)))
                        {
                            File.Delete(file);
                            Console.WriteLine($"Rm {file}");
                        }
                        else
                        {
                            Console.WriteLine($"Dc {file}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Ds {file}");
                    }
                }
            }

            void TryDel(string dirpath)
            {
                if (Directory.Exists(dirpath))
                {
                    if (Directory.GetFiles(dirpath, "*.*", SearchOption.AllDirectories).Length == 0 && Directory.GetDirectories(dirpath, "*", SearchOption.AllDirectories).Length == 0)
                    {
                        Directory.Delete(dirpath);
                        TryDel(Path.GetDirectoryName(dirpath));
                    }
                }
            }

            foreach (var file in Directory.GetDirectories(SRC, "*", SearchOption.AllDirectories))
            {
                TryDel(file);
            }
        }
    }
}
