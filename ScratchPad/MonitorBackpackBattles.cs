﻿using System.Diagnostics;
using System.Threading;
using System.Security.Cryptography;
using System.Linq;
using System.Threading.Tasks;
using System;

while (true)
{
    var cts1 = new CancellationTokenSource(new TimeSpan(0, 4, 00));
    var cts2 = new CancellationTokenSource(new TimeSpan(0, 7, 30));
    if (!Process.GetProcesses().Any(p => p.ProcessName == "steam"))
    {
        Console.WriteLine($"Run Steam");
        new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = @"C:\Program Files (x86)\Steam\steam.exe",
                WorkingDirectory = @"C:\Program Files (x86)\Steam"
            }
        }.Start();
        await Task.Delay(20000);
    }
    var p = new Process
    {
        StartInfo = new ProcessStartInfo
        {
            FileName = @"C:\Projects\GodotRe\BackpackBattlesRankSpider\ScratchPad.exe",
            Arguments = "0 80000 5000 300",
            WorkingDirectory = @"C:\Projects\GodotRe\BackpackBattlesRankSpider",
            UseShellExecute = false,
            RedirectStandardOutput = true,
            CreateNoWindow = true
        }
    };
    var t = DateTime.Now;
    Console.WriteLine($"Start: {DateTime.Now}");
    p.OutputDataReceived += (obj, e) =>
    {
        Console.WriteLine(e.Data);
    };
    p.Start();
    p.BeginOutputReadLine();
    try
    {
        await p.WaitForExitAsync(cts1.Token);
    }
    catch
    {
    }
    if (!p.HasExited)
    {
        p.Kill();
        Console.WriteLine($"Steam crashed: {DateTime.Now}");
        continue;
    }
    Console.WriteLine($"WaitForExit: {DateTime.Now}");
    if (p.ExitCode != 0)
    {
        Console.WriteLine($"ExitCode: {p.ExitCode}");
        continue;
    }
    try
    {
        await Task.Delay(new TimeSpan(0, 20, 0), cts2.Token);
    }
    catch
    {
    }
}