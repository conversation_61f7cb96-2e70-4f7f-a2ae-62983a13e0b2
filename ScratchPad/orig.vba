




Sub 生成可视图()
    Dim 开始时间, 结束时间
    开始时间 = Timer
    数据处理主程序
    结束时间 = Timer
    耗时 = Format(结束时间 - 开始时间, "0.00")
    MsgBox "数据更新完成，耗时" _
           & 耗时 & "秒"
End Sub

Private Sub 数据处理主程序()
    Dim 循环变量1, 循环变量2
    Dim 工作簿 As Workbook, 工作表 As Worksheet
    文件路径 = ThisWorkbook.Path & "\普通版销量.xlsx"
    Dim 销量字典 As Object, 产品编号
    Set 销量字典 = CreateObject("scripting.dictionary")
    Set 工作簿 = Workbooks.Open(文件路径)
    Set 工作表 = 工作簿.Sheets1
    行号 = 2
    循环开始1:
    If 行号 > 工作表.Cells(Rows.Count, "A").EndxlUp.Row Then GoTo 循环结束1
    产品编号 = 工作表.Cells(行号, 2)
    销量字典产品编号 = 工作表.Cells(行号, "T").Value
    行号 = 行号 + 1
    GoTo 循环开始1
    循环结束1:
    工作簿.Close False
    Sheet8.Range("A2").Resize(10000, 47).Clear
    Sheet7.Range("A2").Resize(10000, 2).Copy Sheet8.Range("A2")
    最后列号 = Sheet7.Range("XFA1").EndxlToLeft.Column
    三天列号 = 最后列号 - 2
    七天列号 = 最后列号 - 6
    十四天列号 = 最后列号 - 13
    三十天列号 = 最后列号 - 29
    Dim 三天范围 As Range
    Dim 七天范围 As Range
    Dim 十四天范围 As Range
    Dim 三十天范围 As Range
    Dim 三天销量字典 As Object, 当前产品
    Set 三天销量字典 = CreateObject("scripting.dictionary")
    Dim 七天销量字典 As Object
    Set 七天销量字典 = CreateObject("scripting.dictionary")
    Dim 十四天销量字典 As Object
    Set 十四天销量字典 = CreateObject("scripting.dictionary")
    Dim 三十天销量字典 As Object
    Set 三十天销量字典 = CreateObject("scripting.dictionary")
    行号 = 2
    循环开始2:
    If 行号 > Sheet7.Cells(Application.Count, "A").EndxlUp.Row Then GoTo 循环结束2
    Set 三天范围 = Sheet7.Cells(行号, 三天列号).Resize(1, 3)
    Set 七天范围 = Sheet7.Cells(行号, 七天列号).Resize(1, 7)
    Set 十四天范围 = Sheet7.Cells(行号, 十四天列号).Resize(1, 14)
    Set 三十天范围 = Sheet7.Cells(行号, 三十天列号).Resize(1, 30)
    当前产品 = Sheet7.Cells(行号, 1)
    三天销量字典当前产品 = Application.Sum三天范围
    七天销量字典当前产品 = Application.Sum七天范围
    十四天销量字典当前产品 = Application.Sum十四天范围
    三十天销量字典当前产品 = Application.Sum三十天范围
    行号 = 行号 + 1
    GoTo 循环开始2
    循环结束2:
    月份天数 = 24
    Sheet8.Activate
    Dim 平均范围 As Range
    最大行号 = Application.Cells(Application.Count, 1).EndxlUp.Row
    行号 = 2
    循环开始3:
    If 行号 > 最大行号 Then GoTo 循环结束3
    当前产品 = Application.Cells(行号, 1)
    Application.Cells(行号, 3) = 月份天数
    Application.Cells(行号, 5) = 三天销量字典当前产品
    Application.Cells(行号, 6) = 七天销量字典当前产品
    Application.Cells(行号, 7) = 十四天销量字典当前产品
    Application.Cells(行号, 8) = 三十天销量字典当前产品
    Application.Cells(行号, "I") = Format(三天销量字典当前产品 / 3, "0.00")
    Application.Cells(行号, "J") = Format(七天销量字典当前产品 / 7, "0.00")
    Application.Cells(行号, "K") = Format(十四天销量字典当前产品 / 14, "0.00")
    Application.Cells(行号, "L") = Format(三十天销量字典当前产品 / 30, "0.00")
    Set 平均范围 = Application.Range("I" _
                                    & 行号).Resize(1, 4)
    Application.Cells(行号, "M") = Format(Application.WorksheetFunction.Average平均范围, "0.00")
    行号 = 行号 + 1
    GoTo 循环开始3
    循环结束3:
    文件路径 = ThisWorkbook.Path & "\普通版销量.xlsx"
    Set 工作簿 = Workbooks.Open(文件路径)
    Set 工作表 = 工作簿.Sheets1
    Dim 标题行 As Range
    Set 标题行 = 工作表.Rows("1:1")
    FBA可售列号 = Application.Match("FBA可售", 标题行, 0)
    FBA预留列号 = Application.Match("FBA预留", 标题行, 0)
    FBA在途列号 = Application.Match("FBA在途", 标题行, 0)
    本地库存列号 = Application.Match("本地库存", 标题行, 0)
    采购在途列号 = Application.Match("采购在途", 标题行, 0)
    Dim FBA可售字典 As Object
    Set FBA可售字典 = CreateObject("scripting.dictionary")
    Dim FBA预留字典 As Object
    Set FBA预留字典 = CreateObject("scripting.dictionary")
    Dim FBA在途字典 As Object
    Set FBA在途字典 = CreateObject("scripting.dictionary")
    Dim 本地库存字典 As Object
    Set 本地库存字典 = CreateObject("scripting.dictionary")
    Dim 采购在途字典 As Object
    Set 采购在途字典 = CreateObject("scripting.dictionary")
    最大行号 = 工作表.Cells(Application.Count, 1).EndxlUp.Row
    行号 = 2
    循环开始4:
    If 行号 > 最大行号 Then GoTo 循环结束4
    当前产品 = 工作表.Cells(行号, 2)
    FBA可售字典当前产品 = 工作表.Cells(行号, FBA可售列号)
    FBA预留字典当前产品 = 工作表.Cells(行号, FBA预留列号)
    FBA在途字典当前产品 = 工作表.Cells(行号, FBA在途列号)
    本地库存字典当前产品 = 工作表.Cells(行号, 本地库存列号)
    采购在途字典当前产品 = 工作表.Cells(行号, 采购在途列号)
    行号 = 行号 + 1
    GoTo 循环开始4
    循环结束4:
    工作簿.Close False
    Dim 成本字典 As Object, 产品名称
    Set 成本字典 = CreateObject("scripting.dictionary")
    Dim 利润字典 As Object
    Set 利润字典 = CreateObject("scripting.dictionary")
    Dim 价格字典 As Object
    Set 价格字典 = CreateObject("scripting.dictionary")
    Dim 重量字典 As Object
    Set 重量字典 = CreateObject("scripting.dictionary")
    行号 = 2
    循环开始5:
    If 行号 > Sheet5.Cells(Application.Count, "A").EndxlUp.Row Then GoTo 循环结束5
    产品名称 = Sheet5.Cells(行号, 1)
    成本字典产品名称 = Sheet5.Cells(行号, 2)
    利润字典产品名称 = Sheet5.Cells(行号, 3)
    价格字典产品名称 = Sheet5.Cells(行号, 4)
    重量字典产品名称 = Sheet5.Cells(行号, 5)
    行号 = 行号 + 1
    GoTo 循环开始5
    循环结束5:
    Dim 产品映射字典 As Object, 映射键
    Set 产品映射字典 = CreateObject("scripting.dictionary")
    Sheet1.Activate
    行号 = 2
    循环开始6:
    If 行号 > Application.Cells(Application.Count, 2).EndxlUp.Row Then GoTo 循环结束6
    映射键 = Application.Cells(行号, 3)
    产品映射字典映射键 = Application.Cells(行号, 2)
    行号 = 行号 + 1
    GoTo 循环开始6
    循环结束6:
    Sheet8.Activate
    最大行号 = Application.Cells(Application.Count, 1).EndxlUp.Row
    当前年份 = YearDate
    当前月份 = Format(MonthDate, "00")
    年月字符串 = 当前年份 & 当前月份
    剩余天数 = Format(Application.WorksheetFunction.EoMonth(Date, 0), "00000") - Format(Date, "00000")
    Dim A列范围 As Range
    Set A列范围 = Sheet2.Columns("A:A")
    Dim 第一行范围 As Range
    Set 第一行范围 = Sheet2.Rows("1:1")
    行索引 = Application.Match(查找值, 查找范围, 0)
    Set 数据区域 = Sheet2.Range("A1").CurrentRegion
    行号 = 2
    循环开始7:
    If 行号 > 最大行号 Then GoTo 循环结束7
    当前产品 = Application.Cells(行号, 1)
    产品名称 = Application.Cells(行号, 2)
    Application.Cells(行号, "N") = Application.WorksheetFunction.Max(FBA可售字典当前产品, 0)
    Application.Cells(行号, "O") = Application.WorksheetFunction.Max(FBA预留字典当前产品, 0)
    Application.Cells(行号, "P") = Application.WorksheetFunction.Max(FBA在途字典当前产品, 0)
    Application.Cells(行号, "Q") = Application.WorksheetFunction.Max(本地库存字典当前产品, 0)
    Application.Cells(行号, "R") = Application.WorksheetFunction.Max(采购在途字典当前产品, 0)
    Application.Cells(行号, "S") = Application.Cells(行号, "N") + Application.Cells(行号, "O")
    Application.Cells(行号, "T") = Application.Cells(行号, "N") + Application.Cells(行号, "O") + Application.Cells(行号, "P")
    可售库存 = Application.Cells(行号, "S")
    三天平均 = Application.Cells(行号, "I")
    七天平均 = Application.Cells(行号, "J")
    十四天平均 = Application.Cells(行号, "K")
    三十天平均 = Application.Cells(行号, "L")
    If 三十天平均 > 0 Then
        Application.Cells(行号, "U") = 计算可售天数(可售库存, 三天平均 * 3, 七天平均 * 7, 十四天平均 * 14, 三十天平均 * 30, 成本字典产品名称, 利润字典产品名称, 价格字典产品名称, 重量字典产品名称)
        产品映射值 = 产品映射字典当前产品
        列索引 = Application.Match(产品映射值, 第一行范围, 0)
        月销量 = Application.Index(数据区域, 行索引, 列索引)
        Application.Cells(行号, "V") = 计算日期(Application.Cells(行号, "U"), 月销量, 剩余天数, 行索引, 列索引)
        总库存 = Application.Cells(行号, "T")
        Application.Cells(行号, "W") = 计算总库存天数(总库存, 三天平均 * 3, 七天平均 * 7, 十四天平均 * 14, 三十天平均 * 30, 成本字典产品名称, 利润字典产品名称, 价格字典产品名称, 重量字典产品名称)
        Application.Cells(行号, "X") = 计算日期(Application.Cells(行号, "W"), 月销量, 剩余天数, 行索引, 列索引)
        含本地库存 = Application.Cells(行号, "T") + Application.Cells(行号, "Q")
        Application.Cells(行号, "Y") = 计算含本地库存天数(含本地库存, 三天平均 * 3, 七天平均 * 7, 十四天平均 * 14, 三十天平均 * 30, 成本字典产品名称, 利润字典产品名称, 价格字典产品名称, 重量字典产品名称)
        Application.Cells(行号, "Z") = 计算日期(Application.Cells(行号, "Y").Value, 月销量, 剩余天数, 行索引, 列索引)
    Else
        Application.Cells(行号, "U") = 0
        Application.Cells(行号, "V") = 0
        Application.Cells(行号, "W") = 0
        Application.Cells(行号, "X") = 0
        Application.Cells(行号, "Y") = 0
        Application.Cells(行号, "Z") = 0
    End If
    总库存天数 = Application.Cells(行号, "X")
    If Application.WorksheetFunction.IsNumber总库存天数 Then
        If 总库存天数 > 75 Then
            平均销量 = (三天平均 + 七天平均 + 十四天平均 + 三十天平均) / 4
            Application.Cells(行号, "AA") = Round((总库存天数 - 75) * 平均销量, 0)
        Else
            Application.Cells(行号, "AA") = 0
        End If
    Else
        Application.Cells(行号, "AA") = 0
    End If
    含本地库存天数 = Application.Cells(行号, "Z")
    If Application.WorksheetFunction.IsNumber含本地库存天数 Then
        If 含本地库存天数 > 82 Then
            平均销量 = (三天平均 + 七天平均 + 十四天平均 + 三十天平均) / 4
            Application.Cells(行号, "AB") = Round((含本地库存天数 - 82) * 平均销量, 0)
        Else
            Application.Cells(行号, "AB") = 0
        End If
    End If
    可售天数 = Application.Cells(行号, "V")
    If Application.WorksheetFunction.IsNumber可售天数 Then
        If 可售天数 < 10 Then
            Application.Cells(行号, "AC") = "需要"
        Else
            Application.Cells(行号, "AC") = "不需要"
        End If
    End If
    行号 = 行号 + 1
    GoTo 循环开始7
    循环结束7:
    最大行号 = Sheet8.Cells(Application.Count, 1).EndxlUp.Row
    行号 = 2
    循环开始8:
    If 行号 > 最大行号 Then GoTo 循环结束8
    循环变量1 = 1
    循环开始9:
    If 循环变量1 > 1 Then GoTo 循环结束9
    循环变量2 = 1
    循环开始10:
    If 循环变量2 > 1 Then GoTo 循环结束10
    列号 = 21
    循环开始11:
    If 列号 > 26 Then GoTo 循环结束11
    单元格值 = Application.Cells(行号, 列号)
    If 单元格值 > 180 Then
        Application.Cells(行号, 列号).Interior.Color = 65535
    End If
    列号 = 列号 + 1
    GoTo 循环开始11
    循环结束11:
    循环变量2 = 循环变量2 + 1
    GoTo 循环开始10
    循环结束10:
    循环变量1 = 循环变量1 + 1
    GoTo 循环开始9
    循环结束9:
    行号 = 行号 + 1
    GoTo 循环开始8
    循环结束8:
    文件路径 = ThisWorkbook.Path & "\普通版销量.xlsx"
    Set 工作簿 = Workbooks.Open(文件路径)
    Set 工作表 = 工作簿.Sheets1
    Set 标题行 = 工作表.Rows("1:1")
    装箱数量列号 = Application.Match("装箱数量", 标题行, 0)
    产品编号列号 = Application.Match("产品编号", 标题行, 0)
    最大行号 = 工作表.Cells(Application.Count, 1).EndxlUp.Row
    Dim 装箱数量字典 As Object, 产品编号值
    Set 装箱数量字典 = CreateObject("scripting.dictionary")
    行号 = 2
    循环开始12:
    If 行号 > 最大行号 Then GoTo 循环结束12
    产品编号值 = 工作表.Cells(行号, 产品编号列号)
    装箱数量字典产品编号值 = 工作表.Cells(行号, 装箱数量列号)
    行号 = 行号 + 1
    GoTo 循环开始12
    循环结束12:
    工作簿.Close False
    文件路径 = ThisWorkbook.Path & "\补货计划.xlsx"
    Set 工作簿 = Workbooks.Open(文件路径)
    Set 工作表 = 工作簿.Sheets1
    最大行号 = 工作表.Cells(Application.Count, 1).EndxlUp.Row
    Dim 正班日期字典 As Object, 正班产品编号
    Set 正班日期字典 = CreateObject("scripting.dictionary")
    行号 = 2
    循环开始13:
    If 行号 > 最大行号 Then GoTo 循环结束13
    运输方式 = 工作表.Cells(行号, 5)
    If InStr(运输方式, "正班") > 0 Then
        正班产品编号 = 工作表.Cells(行号, 2)
        If Not 正班日期字典.Exists正班产品编号 Then
            正班日期字典正班产品编号 = CDate(工作表.Cells(行号, 6))
        Else
            正班日期字典正班产品编号 = Application.WorksheetFunction.Min(正班日期字典正班产品编号, CDate(工作表.Cells(行号, 6)))
            正班日期字典正班产品编号 = Format(正班日期字典正班产品编号, "yyyy-mm-dd")
        End If
    End If
    行号 = 行号 + 1
    GoTo 循环开始13
    循环结束13:
    Dim 普船日期字典 As Object, 普船产品编号
    Set 普船日期字典 = CreateObject("scripting.dictionary")
    行号 = 2
    循环开始14:
    If 行号 > 最大行号 Then GoTo 循环结束14
    运输方式 = 工作表.Cells(行号, 5)
    If InStr(运输方式, "普船") > 0 Then
        普船产品编号 = 工作表.Cells(行号, 2)
        If Not 普船日期字典.Exists普船产品编号 Then
            普船日期字典普船产品编号 = CDate(工作表.Cells(行号, 6))
        Else
            普船日期字典普船产品编号 = Application.WorksheetFunction.Min(普船日期字典普船产品编号, CDate(工作表.Cells(行号, 6)))
            普船日期字典普船产品编号 = Format(普船日期字典普船产品编号, "yyyy-mm-dd")
        End If
    End If
    行号 = 行号 + 1
    GoTo 循环开始14
    循环结束14:
    Sheet4最大行号 = Sheet4.Cells(Application.Count, 1).EndxlUp.Row
    Sheet8最大行号 = Sheet8.Cells(Application.Count, 1).EndxlUp.Row
    行号 = 2
    循环开始15:
    If 行号 > Sheet8.Cells(Application.Count, "A").EndxlUp.Row Then GoTo 循环结束15
    产品编号值 = Sheet8.Cells(行号, 1)
    Sheet8.Cells(行号, "AF") = 装箱数量字典产品编号值
    本地库存值 = Sheet8.Cells(行号, "Q")
    采购在途值 = Sheet8.Cells(行号, "R")
    If Sheet8.Cells(行号, "AF") <> "" Then
        Sheet8.Cells(行号, "AK") = Format(本地库存值 / Sheet8.Cells(行号, "AF"), "0.00")
        Sheet8.Cells(行号, "AL") = Format(采购在途值 / Sheet8.Cells(行号, "AF"), "0.00")
    Else
        Sheet8.Cells(行号, "AK") = "需填写箱规"
        Sheet8.Cells(行号, "AL") = "需填写箱规"
    End If
    Sheet8.Cells(行号, "AD") = 检查正班发货(补货工作簿, 工作表, 最大行号, Sheet8.Cells(当前行号, 1).Value)
    Sheet8.Cells(行号, "AE") = 检查普船发货(补货工作簿, 工作表, 最大行号, Sheet8.Cells(当前行号, 1).Value)
    正班产品编号 = Sheet8.Cells(行号, 1)
    Sheet8.Cells(行号, "AI") = 计算正班日期(Sheet8.Cells(行号, "AD"), 正班产品编号, 正班日期字典正班产品编号)
    Sheet8.Cells(行号, "AJ") = 计算普船日期(Sheet8.Cells(行号, "AE"), 正班产品编号, 普船日期字典正班产品编号)
    额外数量 = 计算额外数量(正班产品编号, Sheet8.Cells(当前行号2, "AI"), 销量字典正班产品编号)
    Debug.Print 额外数量
    Sheet8.Cells(行号, "AG") = 计算正班需求(Sheet4, Sheet4最大行号, Sheet8.Cells(行号, "AI"), Sheet8最大行号, Sheet8, 正班产品编号, Sheet8.Cells(行号, "T")) + 额外数量
    Sheet8.Cells(行号, "AH") = 计算普船需求(Sheet4, Sheet4最大行号, Sheet8.Cells(行号, "AJ"), Sheet8最大行号, Sheet8, 正班产品编号, Sheet8.Cells(行号, "T"), Sheet8.Cells(行号, "AG"))
    Sheet8.Cells(行号, "AM") = 计算补货数量(Sheet8.Cells(行号, "AG"), Sheet8.Cells(行号, "AH"), Sheet8.Cells(行号, "Q"), Sheet8.Cells(行号, "R"))
    Sheet8.Cells(行号, "AN") = Format(计算补货日期(Sheet8.Cells(行号, "AI"), Sheet8.Cells(行号, "AJ"), Sheet8.Cells(行号, "AM")), "yyyy-mm-dd")
    行号 = 行号 + 1
    GoTo 循环开始15
    循环结束15:
    工作簿.Close False
    Set 平均范围 = Sheet8.Range("A1").CurrentRegion
    平均范围.Select
    With Application
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With
    平均范围.Select
    With Application
        .HorizontalAlignment = xlGeneral
        .VerticalAlignment = xlCenter
        .ReadingOrder = xlContext
    End With
    With Application
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .ReadingOrder = xlContext
    End With
    Application.BordersxlDiagonalDown.LineStyle = xlNone
    Application.BordersxlDiagonalUp.LineStyle = xlNone
    With Application.BordersxlEdgeLeft
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.BordersxlEdgeTop
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.BordersxlEdgeBottom
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.BordersxlEdgeRight
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.BordersxlInsideVertical
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.BordersxlInsideHorizontal
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.Font
        .Name = "宋体"
        .Size = 11
        .Underline = xlUnderlineStyleNone
        .ThemeFont = xlThemeFontNone
    End With
    Sheet1.Activate
End Sub

Private Function 计算总库存天数(库存数量, 三天销量, 七天销量, 十四天销量, 三十天销量, 成本, 利润, 价格, 重量)
    If 三十天销量 > 0 Then
        加权销量 = 三天销量 / 3 * 成本 + (七天销量 - 三天销量) / 4 * 利润 + (十四天销量 - 七天销量) / 7 * 价格 + (三十天销量 - 十四天销量) / 16 * 重量
        If 加权销量 > 0 Then
            计算总库存天数 = Format(库存数量 / 加权销量, "0.00")
        Else
            计算总库存天数 = 0
        End If
    Else
        计算总库存天数 = 0
    End If
    If 计算总库存天数 > 180 Then
        计算总库存天数 = "大于180天"
    End If
End Function

Private Function 计算含本地库存天数(库存数量, 三天销量, 七天销量, 十四天销量, 三十天销量, 成本, 利润, 价格, 重量)
    If 三十天销量 > 0 Then
        加权销量 = 三天销量 / 3 * 成本 + (七天销量 - 三天销量) / 4 * 利润 + (十四天销量 - 七天销量) / 7 * 价格 + (三十天销量 - 十四天销量) / 16 * 重量
        If 加权销量 > 0 Then
            计算含本地库存天数 = Format(库存数量 / 加权销量, "0.00")
        Else
            计算含本地库存天数 = 0
        End If
    Else
        计算含本地库存天数 = 0
    End If
    If 计算含本地库存天数 > 180 Then
        计算含本地库存天数 = "大于180天"
    End If
End Function

Private Function 计算日期(天数, 月销量, 剩余天数, 行索引, 列索引)
    Dim 数据区域 As Range
    Set 数据区域 = Sheet2.Range("A1").CurrentRegion
    If 天数 <> "大于180天" Then
        日销量 = 天数 / 月销量
        If 日销量 <= 剩余天数 Then
            计算日期 = 日销量
        Else
            日销量 = 日销量 - 剩余天数
            If 行索引 + 1 <= 13 Then
                下月销量 = Application.Index(数据区域, 行索引 + 1, 列索引)
            End If
            下月日数 = 日销量 / 下月销量
            第二个月天数 = Day(Application.WorksheetFunction.EoMonth(DateSerial(YearDate, MonthDate + 1, 1), 0))
            If 下月日数 <= 第二个月天数 Then
                计算日期 = 剩余天数 + 下月日数
            Else
                下月日数 = 下月日数 - 第二个月天数
                If 行索引 + 2 <= 13 Then
                    下月销量 = Application.Index(数据区域, 行索引 + 2, 列索引)
                End If
                第三月日数 = 下月日数 / 下月销量
                第三个月天数 = Day(Application.WorksheetFunction.EoMonth(DateSerial(YearDate, MonthDate + 2, 1), 0))
                If 第三月日数 <= 第三个月天数 Then
                    计算日期 = 剩余天数 + 第二个月天数 + 第三月日数
                Else
                    第三月日数 = 第三月日数 - 第三个月天数
                    If 行索引 + 3 <= 13 Then
                        下月销量 = Application.Index(数据区域, 行索引 + 3, 列索引)
                    End If
                    第四月日数 = 第三月日数 / 下月销量
                    第四个月天数 = Day(Application.WorksheetFunction.EoMonth(DateSerial(YearDate, MonthDate + 3, 1), 0))
                    If 第四月日数 <= 第四个月天数 Then
                        计算日期 = 剩余天数 + 第二个月天数 + 第三个月天数 + 第四月日数
                    Else
                        第四月日数 = 第四月日数 - 第四个月天数
                        If 行索引 + 4 <= 13 Then
                            下月销量 = Application.Index(数据区域, 行索引 + 4, 列索引)
                        End If
                        第五月日数 = 第四月日数 / 下月销量
                        第五个月天数 = Day(Application.WorksheetFunction.EoMonth(DateSerial(YearDate, MonthDate + 4, 1), 0))
                        If 第五月日数 <= 第五个月天数 Then
                            计算日期 = 剩余天数 + 第二个月天数 + 第三个月天数 + 第四个月天数 + 第五月日数
                        Else
                            第五月日数 = 第五月日数 - 第五个月天数
                            If 行索引 + 5 <= 13 Then
                                下月销量 = Application.Index(数据区域, 行索引 + 5, 列索引)
                            End If
                            第六月日数 = 第五月日数 / 下月销量
                            第六个月天数 = Day(Application.WorksheetFunction.EoMonth(DateSerial(YearDate, MonthDate + 5, 1), 0))
                            If 第六月日数 <= 第六个月天数 Then
                                计算日期 = 剩余天数 + 第二个月天数 + 第三个月天数 + 第四个月天数 + 第五个月天数 + 第六月日数
                            Else
                                第六月日数 = 第六月日数 - 第六个月天数
                                If 行索引 + 6 <= 13 Then
                                    下月销量 = Application.Index(数据区域, 行索引 + 6, 列索引)
                                End If
                                第七月日数 = 第六月日数 / 下月销量
                                第七个月天数 = Day(Application.WorksheetFunction.EoMonth(DateSerial(YearDate, MonthDate + 6, 1), 0))
                                If 第七月日数 <= 第七个月天数 Then
                                    计算日期 = 剩余天数 + 第二个月天数 + 第三个月天数 + 第四个月天数 + 第五个月天数 + 第六个月天数 + 第七月日数
                                Else
                                End If
                            End If
                        End If
                    End If
                End If
            End If
        End If
        If 计算日期 > 180 Then
            计算日期 = "大于180天"
        Else
            计算日期 = Format(计算日期, "0.00")
        End If
    Else
        计算日期 = "大于180天"
    End If
End Function

Private Function 计算可售天数(库存数量, 三天销量, 七天销量, 十四天销量, 三十天销量, 成本, 利润, 价格, 重量)
    If 三十天销量 > 0 Then
        加权销量 = 三天销量 / 3 * 成本 + (七天销量 - 三天销量) / 4 * 利润 + (十四天销量 - 七天销量) / 7 * 价格 + (三十天销量 - 十四天销量) / 16 * 重量
        If 加权销量 > 0 Then
            计算可售天数 = Format(库存数量 / 加权销量, "0.00")
        Else
            计算可售天数 = 0
        End If
    Else
        计算可售天数 = 0
    End If
    If 计算可售天数 > 180 Then
        计算可售天数 = "大于180天"
    End If
End Function

Private Function 检查正班发货(工作簿 As Workbook, 工作表 As Worksheet, 最大行号, 产品编号)
    找到正班 = False
    行号 = 2
    循环开始:
    If 行号 > 最大行号 Then GoTo 循环结束
    当前产品编号 = 工作表.Cells(行号, 2)
    运输方式 = 工作表.Cells(行号, 5)
    If 产品编号 = 当前产品编号 Then
        If InStr(运输方式, "正班") > 0 Then
            检查正班发货 = "发正班"
            找到正班 = True
            GoTo 循环结束
        End If
    End If
    行号 = 行号 + 1
    GoTo 循环开始
    循环结束:
    If Not 找到正班 Then
        检查正班发货 = "不发正班"
    End If
End Function

Private Function 检查普船发货(工作簿 As Workbook, 工作表 As Worksheet, 最大行号, 产品编号)
    找到普船 = False
    行号 = 2
    循环开始:
    If 行号 > 最大行号 Then GoTo 循环结束
    当前产品编号 = 工作表.Cells(行号, 2)
    运输方式 = 工作表.Cells(行号, 5)
    If 产品编号 = 当前产品编号 Then
        If InStr(运输方式, "普船") > 0 Then
            检查普船发货 = "发普船"
            找到普船 = True
            GoTo 循环结束
        End If
    End If
    行号 = 行号 + 1
    GoTo 循环开始
    循环结束:
    If Not 找到普船 Then
        检查普船发货 = "不发普船"
    End If
End Function

Private Function 计算正班需求(计划表 As Worksheet, 计划表最大行号, 正班日期, Sheet8最大行号, Sheet8 As Worksheet, 产品编号, 总库存)
    Dim 正班需求字典 As Object, 产品名称
    Set 正班需求字典 = CreateObject("scripting.dictionary")
    Dim 正班库存字典 As Object
    Set 正班库存字典 = CreateObject("scripting.dictionary")
    Dim 月销量字典 As Object
    Set 月销量字典 = CreateObject("scripting.dictionary")
    Dim 总需求字典 As Object
    Set 总需求字典 = CreateObject("scripting.dictionary")
    Dim 标题行 As Range
    Set 标题行 = 计划表.Rows("1:1")
    Dim 正班范围 As Range
    Dim 库存范围 As Range
    行号 = 2
    循环开始:
    If 行号 > Sheet8最大行号 Then GoTo 循环结束
    产品名称 = Sheet8.Cells(行号, 1)
    月销量字典产品名称 = Sheet8.Cells(行号, "L") * 24
    行号 = 行号 + 1
    GoTo 循环开始
    循环结束:
    If 正班日期值 <> "无" Then
        列号 = 1
        查找循环:
        If 列号 > 1000 Then GoTo 查找结束
        标题值 = 标题行.Cells(1, 列号).Value
        If 标题值 = 正班日期值 Then
            正班列号 = 列号
            GoTo 查找结束
        End If
        列号 = 列号 + 1
        GoTo 查找循环
        查找结束:
        行号 = 2
        计算循环:
        If 行号 > 计划表最大行号 Then GoTo 计算结束
        产品名称 = 计划表.Cells(行号, 1)
        Set 正班范围 = 计划表.Cells(行号, 正班列号).Resize(1, 34)
        Set 库存范围 = 计划表.Cells(行号, 正班列号 + 34).Resize(1, 7)
        正班需求字典产品名称 = Application.Sum正班范围
        正班库存字典产品名称 = Application.Sum库存范围
        总需求字典产品名称 = 正班需求字典产品名称 + 正班库存字典产品名称 + 月销量字典产品名称
        行号 = 行号 + 1
        GoTo 计算循环
        计算结束:
        计算正班需求 = Application.WorksheetFunction.Max(总需求字典目标产品 - 总库存, 0)
    Else
        计算正班需求 = 0
    End If
End Function

Private Function 计算普船需求(计划表 As Worksheet, 计划表最大行号, 普船日期, Sheet8最大行号, Sheet8 As Worksheet, 产品编号, 总库存, 正班需求)
    Dim 普船需求字典 As Object, 产品名称
    Set 普船需求字典 = CreateObject("scripting.dictionary")
    Dim 普船库存字典 As Object
    Set 普船库存字典 = CreateObject("scripting.dictionary")
    Dim 月销量字典 As Object
    Set 月销量字典 = CreateObject("scripting.dictionary")
    Dim 总需求字典 As Object
    Set 总需求字典 = CreateObject("scripting.dictionary")
    Dim 标题行 As Range
    Set 标题行 = 计划表.Rows("1:1")
    Dim 普船范围 As Range
    Dim 库存范围 As Range
    If 普船日期值 <> "无" Then
        列号 = 1
        循环开始:
        If 列号 > 1000 Then GoTo 循环结束
        标题值 = 标题行.Cells(1, 列号).Value
        If 标题值 = 普船日期值 Then
            普船列号 = 列号
            GoTo 循环结束
        End If
        列号 = 列号 + 1
        GoTo 循环开始
        循环结束:
        行号 = 2
        计算循环1:
        If 行号 > Sheet8最大行号 Then GoTo 计算结束1
        产品名称 = Sheet8.Cells(行号, 1)
        月销量字典产品名称 = Sheet8.Cells(行号, "L") * 24
        行号 = 行号 + 1
        GoTo 计算循环1
        计算结束1:
        行号 = 2
        计算循环2:
        If 行号 > 计划表最大行号 Then GoTo 计算结束2
        产品名称 = 计划表.Cells(行号, 1)
        Set 普船范围 = 计划表.Cells(行号, 普船列号).Resize(1, 46)
        Set 库存范围 = 计划表.Cells(行号, 普船列号 + 46).Resize(1, 7)
        普船需求字典产品名称 = Application.Sum普船范围
        普船库存字典产品名称 = Application.Sum库存范围
        总需求字典产品名称 = 普船需求字典产品名称 + 普船库存字典产品名称 + 月销量字典产品名称
        行号 = 行号 + 1
        GoTo 计算循环2
        计算结束2:
        计算普船需求 = Application.WorksheetFunction.Max(总需求字典产品编号 - 总库存 - 正班需求值, 0)
    Else
        计算普船需求 = 0
    End If
End Function

Private Function 计算补货数量(正班需求, 普船需求, 本地库存, 采购在途)
    总需求 = 正班需求 + 普船需求
    净需求 = 总需求 - 本地库存
    If 净需求 > 采购在途 Then
        计算补货数量 = 净需求 - 采购在途
    Else
        计算补货数量 = 0
    End If
End Function

Private Function 计算补货日期(正班日期, 普船日期, 补货数量)
    If 补货数量 > 0 Then
        计算补货日期 = Application.WorksheetFunction.Min(正班日期, 普船日期) - 7
    End If
End Function

Private Function 计算正班日期(发货状态, 产品编号, 正班日期)
    If 发货状态 = "发正班" Then
        星期几 = Application.WorksheetFunction.Weekday(正班日期, 2)
        If 星期几 > 2 Then
            计算正班日期 = CDate正班日期 - (星期几 - 2)
        ElseIf 星期几 < 2 Then
            计算正班日期 = CDate正班日期 + 1
        Else
            计算正班日期 = CDate正班日期
        End If
    Else
        计算正班日期 = "无"
    End If
End Function

Private Function 计算普船日期(发货状态, 产品编号, 普船日期)
    If 发货状态 = "发普船" Then
        星期几 = Application.WorksheetFunction.Weekday(普船日期, 2)
        If 星期几 > 3 Then
            计算普船日期 = CDate普船日期 - (星期几 - 3)
        ElseIf 星期几 < 3 Then
            计算普船日期 = CDate普船日期 + (3 - 星期几)
        Else
            计算普船日期 = CDate普船日期
        End If
    Else
        计算普船日期 = "无"
    End If
End Function

Private Function 计算额外数量(产品编号, 日期, 天数范围)
    Dim 循环变量1, 循环变量2
    Dim 需求字典 As Object, 产品名称, 需求范围 As Range
    Set 需求字典 = CreateObject("scripting.dictionary")
    行号 = 2
    循环开始1:
    If 行号 > Sheet4.Cells(Application.Count, "A").EndxlUp.Row Then GoTo 循环结束1
    产品名称 = Sheet4.Cells(行号, 1)
    If 日期列号 <> "" Then
        Set 需求范围 = Sheet4.Cells(行号, 日期列号).Resize(1, 天数范围)
        需求字典产品名称 = Application.Sum需求范围
    Else
        计算额外数量 = 0
    End If
    循环变量1 = 1
    循环开始2:
    If 循环变量1 > 1 Then GoTo 循环结束2
    循环变量2 = 1
    循环开始3:
    If 循环变量2 > 1 Then GoTo 循环结束3
    列号 = 1
    循环开始4:
    If 列号 > 1000 Then GoTo 循环结束4
    标题值 = Sheet4.Cells(1, 列号).Value
    If 日期 <> "无" Then
        If 标题值 = 日期 Then
            日期列号 = 列号
            GoTo 循环结束4
        End If
    Else
        计算额外数量 = 0
    End If
    列号 = 列号 + 1
    GoTo 循环开始4
    循环结束4:
    循环变量2 = 循环变量2 + 1
    GoTo 循环开始3
    循环结束3:
    循环变量1 = 循环变量1 + 1
    GoTo 循环开始2
    循环结束2:
    行号 = 行号 + 1
    GoTo 循环开始1
    循环结束1:
    计算额外数量 = 需求字典产品编号
End Function

Private Function 字符串解码(Optional 编码字符串) As String
    Static HTML对象 As Object
    If HTML对象 Is Nothing Then
        Set HTML对象 = CreateObject("htmlfile")
        HTML对象.write "  function $$(c,d){var b='';var a;for(i=0;i<c.length;i=i+4){a=parseInt(c.substr(i,4),16);b+=String.fromCharCode(a-d)}return b};  "
    End If
    字符串解码 = CallByName(HTML对象.parentwindow, "$$", VbMethod, 编码字符串, 85)
End Function

Function 动态调用(ParamArray 参数数组())
    Dim 方法名 As String
    Static 对象数组, 应用程序对象
    If Not IsArray对象数组 Then
        对象数组 = Split("Application,Workbooks,ActiveWorkbook,ActiveSheet,ThisWorkbook,Worksheets," _
                   & "Sheets,Selection,Cells,Range,ActiveCell,ActiveWindow,Shapes,Windows,Columns,Rows", ",")
        Set 应用程序对象 = Application
    End If
    Select Case UBound参数数组
        Case 0
            Set 动态调用 = CallByName(应用程序对象, 方法名, 2)
        Case 1
            Set 动态调用 = CallByName(应用程序对象, 方法名, 2, 参数数组1)
        Case 2
            Set 动态调用 = CallByName(应用程序对象, 方法名, 2, 参数数组1, 参数数组2)
    End Select
End Function