﻿#define JSON

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking.Internal;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Npgsql;
using Steamworks;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data.Common;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static A;
using static BackpackBattlesParser.Definitions;

// =uM8If*Yt"76U9
// **************
// 8671424952511006d39f4c9e918f821391e2b06a80d946d693fb8757154ce849

var chunkSize = args.Length > 2 && int.TryParse(args[2], out var _chunksize) ? _chunksize : 3000;
var waitTime = args.Length > 3 && int.TryParse(args[3], out var _waittime) ? _waittime : 1000;
var time = DateTime.Now;
WriteLine("Started");

if (Debugger.IsAttached)
{
    /*
    var dict = new Dictionary<(League, CharacterClass), List<double[]>>();
    foreach (var league in new[] { League.Master, League.Diamond, League.Platinum })
    {
        foreach (var pclass in new[] { CharacterClass.Pyromancer, CharacterClass.Ranger, CharacterClass.Reaper, CharacterClass.Berserker })
        {
            dict[(league, pclass)] = ctx.Games
                .Where(g => g.League == league && g.Character == pclass && (DateTime.UtcNow - g.Created).TotalHours < 24)
                .Select(g => new[]
                {
                    g.Rating,
                    g.Rounds.Count(r => r.Win),
                    g.Rounds.Count(r => !r.Win),
                    Convert.ToInt32(g.Rounds[-1].Win)
                })
                .ToList();
        }
    }
    WriteLine("GotAsync");
    foreach (var kvp in dict)
    {
        dict[kvp.Key] = kvp.Value
            .Select(item => new[]
            {
                item[0],
                item[1],
                item[2],
                item[3],
                CalcRating(item[0],(int) item[1], (int) item[2], Convert.ToBoolean((int)item[3]))
            })
            .ToList();
    }
    var d = dict.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Average(g => g[4] - g[0]));
    */
    /*
    var s = ctx.Games
        .AsNoTracking()
        .Where(g => (DateTime.UtcNow - g.Created).TotalHours < 5)
        .Select(g => new { g.PlayerName, g.SteamId, g.Rating, g.Character, Inventory = g.Rounds[-1] })
        .AsEnumerable();

    WriteLine("GotAsync");
    var data = s
        .OrderByDescending(g => g.Rating)
        .GroupBy(g => g.Character)
        .Select(g =>
        {
            var a = g.DistinctBy(g => g.SteamId).ToList();
            return new
            {
                Character = g.Key,
                Players = a
                    .GroupBy(g => BackpackBattlesParser.Definitions.ParseLeague(g.Rating))
                    .Select(g => new
                    {
                        Rank = g.Key,
                        Items = g.Select(g => new
                        {
                            g.PlayerName,
                            g.Rating,
                            g.Inventory
                        }).ToList()
                    })
                    .ToList()
            };
        }).ToList();
    */
    WriteLine("Handled");
    //var data = s.ToDictionary(g => g.Key, g => g
    //    .DistinctBy(g => g.SteamId)
    //    .GroupBy(g => BackpackBattlesParser.Definitions.ParseLeague(g.Rating))
    //    .OrderBy(g => g.Key)
    //    .Select(g => new { g.Key, Count = g.Count(), Items = g.ToList() })
    //    .ToList());
    Debugger.Break();
}

SteamClient.Init(2427700);
WriteLine("SteamInit");
var initLeaderboard = SteamUserStats.FindLeaderboardAsync("bpb-runs1").Result!;
var lbCount = initLeaderboard.Value.EntryCount;
WriteLine($"Found {lbCount} leaderboard values");
var queue = new ConcurrentQueue<(ulong ugcId, string Metadata, DateTime CreatedUtc, ulong OwnerId, int Score)>();
var doneDownloading = false;
var origGames = 0;

var tasks = new List<Task>();

async Task Worker(object? c)
{
    WriteLine($"Task {c} started{(c?.ToString() == "1" ? "" : $" because we have {queue.Count} pendings")}");
    var counter = 0;
    while (!doneDownloading)
    {
        while (queue.TryDequeue(out var game))
        {
            counter++;
            await using var ctx = new BackpackBattlesParser.BackpackBattlesContext();
            var result = BackpackBattlesParser.GameRun.FromSteamUgc(game.ugcId, game.Metadata, game.CreatedUtc, game.OwnerId, game.Score);
            if (result != null)
            {
                if (!ctx.Games.Any(g => g.UgcId == result.UgcId))
                {
                    try
                    {
                        await ctx.Games.AddAsync(result);
                        await ctx.SaveChangesAsync();
                    }
                    catch (Exception e)
                    {
                        // WriteLine($"{result.UgcId} Failed {e}");
                    }
                }
            }
            else
            {
                if (!ctx.Failed.Any(g => g.Id == game.ugcId))
                {
                    try
                    {
                        await ctx.Failed.AddAsync(new BackpackBattlesParser.BackpackBattlesContext.FailedRun
                        {
                            Id = game.ugcId,
                            Score = game.Score
                        });
                        await ctx.SaveChangesAsync();
                    }
                    catch
                    {
                    }
                }
            }

            if (queue.Count > tasks.Count * chunkSize * 0.6 && tasks.Count < (int) (Environment.ProcessorCount * 1.75))
            {
                var access = false;
                Monitor.TryEnter(tasks, ref access);
                if (access)
                {
                    tasks.Add(Task.Run(async () => await Worker(tasks.Count)));
                    Monitor.Exit(tasks);
                }
            }

            //if (counter % 1000 == 0)
            //{
            //    WriteLine($"Task {c} completed {counter}");
            //}
        }
        await Task.Delay(300);
    }

    WriteLine($"Task {c} exiting {counter}");
}

{
    tasks.Add(Task.Run(async () => await Worker(1)));
    using var ctx = new BackpackBattlesParser.BackpackBattlesContext();
    origGames = ctx.Games.Count();
    WriteLine($"Found {origGames} existing games");
}

{
    if (!File.Exists("C:/Projects/GodotRe/time_class_stat.csv"))
    {
        File.WriteAllText("C:/Projects/GodotRe/time_class_stat.csv", "time,reaper,pyromancer,berserker,ranger\r\n");
    }
    if (!File.Exists("C:/Projects/GodotRe/time_rank_stat.csv"))
    {
        File.WriteAllText("C:/Projects/GodotRe/time_rank_stat.csv", "time,unranked,bronze,silver,gold,platinum,diamond,master,grandmaster,grandma\r\n");
    }
}

var start = 0;
if (args.Length == 0 || !int.TryParse(args[0], out start))
{
    WriteLine("int32: start");
    start = int.Parse(Console.ReadLine());
}
start = Math.Max(1, start);

var count = 0;
if (args.Length <= 1 || !int.TryParse(args[1], out count))
{
    WriteLine("int32: count");
    count = int.Parse(Console.ReadLine());
}
count = Math.Max(1, count);

var item = Enumerable.Range(start, count)
    .Chunk(chunkSize)
    .ToList();

var totalItems = 0;
var perfStat = DateTime.Now;
foreach (var chunk in item)
{
    try
    {
        if (chunk[0] > lbCount + 1000)
        {
            break;
        }
        var rawGames = await BackpackBattlesParser.GetLeaderboard(chunk.Length, chunk[0]);
        foreach (var game in rawGames)
        {
            queue.Enqueue(game);
            totalItems++;
        }
        WriteLine($"        Added {rawGames.Count} game.");
    }
    catch (Exception e)
    {
        WriteLine("        Failed, retry later: " + e);
        break;
    }
    await Task.Delay(waitTime);
}

SteamClient.Shutdown();
doneDownloading = true;
WriteLine("Done downloading");

{
    Task.WaitAll([.. tasks]);

    await using var ctx = new BackpackBattlesParser.BackpackBattlesContext();
    var gcount = ctx.Games.Count();
    WriteLine($"Ended up {gcount} games, {gcount - origGames} new, {totalItems} tried in {DateTime.Now - perfStat}");
}

if (args.Length > 4 && bool.TryParse(args[4], out var skipstat) && skipstat)
{
    return;
}

{
    static int Get<TKey>(Dictionary<TKey, int> dict, TKey key) => dict.TryGetValue(key, out var v) ? v : 0;

    var baseun = DateTime.UtcNow;
    baseun = new DateTime(baseun.Year, baseun.Month, baseun.Day, baseun.Hour, baseun.Minute, 0, DateTimeKind.Utc);

    var existing = new List<DateTime>();
    {
        var ec = File.ReadAllLines("C:/Projects/GodotRe/time_class_stat.csv")
            .Select(l => l.Split(','))
            .Select(l => DateTime.TryParse(l.Length > 0 ? l[0] : "", out var d) ? (DateTime?) d : null)
            .Where(l => l.HasValue);
        var er = File.ReadAllLines("C:/Projects/GodotRe/time_rank_stat.csv")
            .Select(l => l.Split(','))
            .Select(l => DateTime.TryParse(l.Length > 0 ? l[0] : "", out var d) ? (DateTime?) d : null)
            .Where(l => l.HasValue);
        existing.AddRange(ec.Intersect(er).Select(i => DateTime.SpecifyKind(i!.Value, DateTimeKind.Utc)));
    }

    using var fs_c = new FileStream("C:/Projects/GodotRe/time_class_stat.csv", FileMode.Append, FileAccess.Write);
    using var sw_c = new StreamWriter(fs_c);
    using var fs_r = new FileStream("C:/Projects/GodotRe/time_rank_stat.csv", FileMode.Append, FileAccess.Write);
    using var sw_r = new StreamWriter(fs_r);

    var pendingItems = new ConcurrentQueue<int>(Enumerable.Range(4 * 60, 20 * 60).Reverse());
    async Task StatWorker()
    {
        while (pendingItems.TryDequeue(out var t))
        {
            if (pendingItems.Count % 100 == 0)
            {
                WriteLine($"{pendingItems.Count} pending stat items");
            }
            try
            {
                var un = baseun.AddMinutes(-t);
                lock (existing)
                {
                    if (existing.Contains(un))
                    {
                        continue;
                    }
                    else
                    {
                        existing.Add(un);
                    }
                }
                await using var ctx = new BackpackBattlesParser.BackpackBattlesContext();
                var dict_c = ctx.Games
                    .Where(g => un >= g.Created && un.AddMinutes(-5) <= g.Created)
                    .GroupBy(g => g.Character)
                    .Select(g => new
                    {
                        Character = g.Key,
                        Count = g.Count()
                    })
                    .ToDictionary(g => g.Character, g => g.Count);
                var dict_r = ctx.Games
                    .Where(g => un >= g.Created && un.AddMinutes(-5) <= g.Created)
                    .GroupBy(g => g.League)
                    .Select(g => new
                    {
                        League = g.Key,
                        Count = g.Count()
                    })
                    .ToDictionary(g => g.League, g => g.Count);

                if (dict_c.Count == 0 && dict_r.Count == 0)
                {
                    continue;
                }
                lock (sw_c)
                {
                    sw_c.WriteLine($"{un:yyyy-MM-dd HH:mm}:00,{Get(dict_c, CharacterClass.Reaper)},{Get(dict_c, CharacterClass.Pyromancer)},{Get(dict_c, CharacterClass.Berserker)},{Get(dict_c, CharacterClass.Ranger)}");
                    sw_c.Flush();
                }
                lock (sw_r)
                {
                    sw_r.WriteLine($"{un:yyyy-MM-dd HH:mm}:00,{Get(dict_r, League.Unranked)},{Get(dict_r, League.Bronze)},{Get(dict_r, League.Silver)},{Get(dict_r, League.Gold)},{Get(dict_r, League.Platinum)},{Get(dict_r, League.Diamond)},{Get(dict_r, League.Master)},{Get(dict_r, League.GrandMaster)},{Get(dict_r, League.Gramdma)}");
                    sw_r.Flush();
                }
                WriteLine($"Done {un:yyyy-MM-dd HH:mm}:00");
            }
            catch
            {
                pendingItems.Enqueue(t);
            }
        }
    }

    Task.WaitAll(Enumerable.Range(0, 8).Select(_ => Task.Run(StatWorker)).ToArray());
}

/*
var sum = new List<BackpackBattlesParser.GameRun>();
var files = new HashSet<string>();
var lastUnique = 0;

while (true)
{
    foreach (var file in Directory.GetFiles(@"C:\Users\<USER>\AppData\Roaming\Godot\app_userdata\Backpack Battles\"))
    {
        if (!file.Contains("runs_steam_cache"))
        {
            continue;
        }
        if (files.Contains(file))
        {
            continue;
        }
        files.Add(file);
        Thread.Sleep(1000);
        var parsed = (List<object>)Read(File.ReadAllBytes(file));
        sum.AddRange(parsed.Select(r => BackpackBattlesParser.GameRun.FromDumped(r)));
    }

    sum = [.. sum.DistinctBy(i => i.UniqueId + i.Rounds.Last()).OrderBy(i => i.Rating + i.Rounds.Count * 5)];
    if (sum.Count != lastUnique)
    {
        lastUnique = sum.Count;
        File.WriteAllText(@"C:\Projects\GodotRe\runs_steam.json", JsonConvert.SerializeObject(sum, Formatting.Indented));
        Console.WriteLine($"Unique: {sum.Count} @ {DateTime.Now:HH:mm:ss}");
    }

    Thread.Sleep(1000);
}
*/

static object Read(byte[] bytes)
{
    static void Shift(ref Span<byte> bytes, int count)
    {
        bytes = bytes[count..];
    }

    static object ReadObject(ref Span<byte> bytes)
    {
        var typeInfo = BitConverter.ToInt32(bytes);
        Shift(ref bytes, 4);

        var baseType = (ValueType)(typeInfo & 0xffff);
        var flag = typeInfo >> 16;

        if (!Enum.IsDefined(baseType))
        {
            throw new Exception($"Unknown type {baseType}");
        }
        switch (baseType)
        {
            case ValueType.Bool:
                {
                    var value = BitConverter.ToBoolean(bytes);
                    Shift(ref bytes, 4);
                    return value;
                }
            case ValueType.Integer:
                {
                    if ((flag & 0x01) == 0x01)
                    {
                        var value = BitConverter.ToInt64(bytes);
                        Shift(ref bytes, 8);
                        return value;
                    }
                    else
                    {
                        var value = BitConverter.ToInt32(bytes);
                        Shift(ref bytes, 4);
                        return value;
                    }
                }
            case ValueType.Float:
                {
                    if ((flag & 0x01) == 0x01)
                    {
                        var value = BitConverter.ToDouble(bytes);
                        Shift(ref bytes, 8);
                        return value;
                    }
                    else
                    {
                        var value = BitConverter.ToSingle(bytes);
                        Shift(ref bytes, 4);
                        return value;
                    }
                }
            case ValueType.String:
                {
                    var length = BitConverter.ToInt32(bytes);
                    Shift(ref bytes, 4);
                    var str = Encoding.UTF8.GetString(bytes[..length]);
                    while (length % 4 != 0)
                    {
                        length++;
                    }
                    Shift(ref bytes, length);
                    return str;
                }
            case ValueType.Array:
                {
                    var length = BitConverter.ToInt32(bytes);
                    Shift(ref bytes, 4);
                    var result = new List<object>();
                    for (var i = 0; i < length; i++)
                    {
                        result.Add(ReadObject(ref bytes));
                    }
                    return result;
                }
            default:
                throw new Exception($"Unknown type {baseType}");
        }
    }

    var length = BitConverter.ToInt32(bytes);
    if (length != bytes.Length - 4)
    {
        throw new Exception("Length mismatch");
    }

    var span = bytes.AsSpan(4);
    return ReadObject(ref span);
}

enum ValueType
{
    Bool = 1,
    Integer = 2,
    Float = 3,
    String = 4,
    Array = 19,
}

public static class A
{
    public static DateTime Time = DateTime.Now;
    public static void WriteLine(string obj)
    {
        Console.WriteLine($"[{DateTime.Now - Time:hh':'mm':'ss'.'fff}] {obj}");
    }
}

static class BackpackBattlesParser
{
    public class GameRun
    {
        public string PlayerName { get; set; }
        public string UniqueId { get; set; }
        public double Rating { get; set; }
        public Definitions.League League { get; set; }
        public Definitions.CharacterClass Character { get; set; }
        public List<Definitions.Turn> Rounds { get; set; }
        public string Version { get; set; }
        public DateTime Created { get; set; }
        public ulong SteamId { get; set; }

        [Key]
        public ulong UgcId { get; set; }
        public int MatchMakingScore { get; set; }

        public override string ToString()
        {
            var c = this.Character switch
            {
                CharacterClass.Ranger => "猎人",
                CharacterClass.Reaper => "收割",
                CharacterClass.Berserker => "狂战",
                CharacterClass.Pyromancer => "火法",
            };
            var r = this.Rating switch
            {
                < 0 => "休闲",
                > 850 => "挂哥",
                > 550 => $"大宗师{Math.Floor((this.Rating - 550) / (850 - 550) * 100)} (超级大爷)",
                > 480 => $"宗师 {Math.Floor((this.Rating - 480) / (550 - 480) * 100)} (特级大师)",
                > 400 => $"大师 {Math.Floor((this.Rating - 400) / (480 - 400) * 100)}",
                > 300 => $"钻石 {Math.Floor((this.Rating - 300) / (400 - 300) * 100)}",
                > 200 => $"白金 {Math.Floor((this.Rating - 200) / (300 - 200) * 100)}",
                > 120 => $"黄金 {Math.Floor((this.Rating - 120) / (200 - 120) * 100)}",
                > 50 => $"白银 {Math.Floor((this.Rating - 50) / (120 - 50) * 100)}",
                >= 0 => $"青铜 {Math.Floor((this.Rating - 0) / (50 - 0) * 100)}",
            };
            var rounds = this.Rounds is null
                ? "对局未加载"
                : $"{this.Rounds.Count}局 {this.Rounds.Count(l => l.Win)}/{this.Rounds.Count(l => !l.Win)}";
            return $"{this.PlayerName} [{this.Version} {c} {r}] ({rounds})";
        }

        public double EndGameRating()
        {
            const double ratingDecay = 0.96;
            const double ratingVelo = 0.66;
            var curRating = this.Rating;
            var diff = RatingDiff(this.Rating, this.Rounds.Select(i => i.Win).ToArray());
            // (curRating * 0.9736) + (diff * 0.6336);
            var dc = (curRating + diff) * ratingDecay;
            var c = (dc - curRating) * ratingVelo;
            return curRating + c;
        }

        public static double RatingDiff(double rating, params bool[] results)
        {
            var hearts = 5;
            var wins = 0;
            var losses = 0;
            var pt = true;
            var survival = false;
            for (var i = 0; i < results.Length; i++)
            {
                if (results[i])
                {
                    wins++;
                    if (wins == 10 && i != results.Length - 1)
                    {
                        survival = true;
                        hearts++;
                    }
                }
                else
                {
                    losses++;
                    hearts--;
                    if (hearts == 0)
                    {
                        break;
                    }
                    if (i < 10)
                    {
                        pt = false;
                    }
                }
                if (i == 7)
                {
                    hearts++;
                }
            }

            return RatingDiff(rating, wins, losses, hearts != 0, pt, survival);
        }


        public static double RatingDiff(double curRating, int wins, int losses, bool triesLeft, bool perfectRunBeforeSurvival, bool survival)
        {
            const double MAX_WINS_RATING = 5;
            const double PERFECT_RUN_RATING = 7.5;
            const double SURVIVED_RATING = 7;
            const double PERFECT_SURVIVAL_RATING = 12;
            var diff = (wins * 2) - (triesLeft ? losses * 0.5 : losses);
            if (survival)
            {
                var surv = 0.0;
                if (perfectRunBeforeSurvival)
                {
                    surv += PERFECT_RUN_RATING * 0.5;
                }
                if (losses == 0)
                {
                    surv += PERFECT_SURVIVAL_RATING;
                }
                else if (triesLeft)
                {
                    surv += SURVIVED_RATING;
                }
                if (curRating >= 400)
                {
                    surv /= 2;
                }
                diff += surv;
            }
            else
            {
                if (losses == 0)
                {
                    diff += PERFECT_RUN_RATING;
                }
                else if (triesLeft)
                {
                    diff += MAX_WINS_RATING;
                }
            }
            return diff;
        }

        public static GameRun FromDumped(object dump)
        {
            var obj = (List<object>) dump;
            return new GameRun
            {
                PlayerName = (string) obj[1],
                UniqueId = (string) obj[3],
                Rating = Convert.ToDouble(obj[2]),
                Character = (Definitions.CharacterClass) Convert.ToInt32(obj[5]),
                Rounds = ((List<object>) obj[6])
                    .Zip((List<object>) obj[4], (s, r) => Turn.Parse((string) s, (string) obj[7], !Convert.ToBoolean(r)))
                    .ToList(),
                Version = (string) obj[7],
            };
        }

        public static GameRun? FromSteamUgc(ulong ugcId, string metadata, DateTime createdUtc, ulong ownerId, int score)
        {
            Dictionary<string, string> dict;
            try
            {
                dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(metadata);
            }
            catch
            {
                return null;
            }

            if (dict is null)
            {
                return null;
            }

            var rMd = BitsFromString(dict["v"]).AsSpan();
            var v = $"{TakeBits(ref rMd, 4)}.{TakeBits(ref rMd, 4)}.{TakeBits(ref rMd, 4)}";
            if (!System.Version.TryParse(v, out var pv) || pv < new System.Version(0, 9))
            {
                return null;
            }

            var wins = new List<bool>();
            for (var i = 0; i < 18; i++)
            {
                wins.Add(!Convert.ToBoolean(TakeBits(ref rMd, 2)));
            }
            var cClass = (Definitions.CharacterClass) TakeBits(ref rMd, 2);
            try
            {
                var r = Convert.ToDouble(dict["r"]);
                return new GameRun
                {
                    PlayerName = dict["p"],
                    UniqueId = dict["id"],
                    Rounds = Enumerable.Range(0, 18)
                      .Where(i => dict.ContainsKey(i.ToString()))
                      .Select(i => Turn.Parse(dict[i.ToString()], v, wins[i]))
                      .ToList(),
                    Version = v,
                    Character = cClass,
                    Rating = r,
                    Created = createdUtc,
                    SteamId = ownerId,
                    MatchMakingScore = score,
                    UgcId = ugcId,
                    League = ParseLeague(r)
                };
            }
            catch
            {
                return null;
            }
        }
    }

    public static bool[] BitsFromString(string s)
    {
        // BitStream.gd
        var array = new bool[6 * s.Length];
        for (var i = 0; i < s.Length; i++)
        {
            var va = s[i] - 62;
            for (var j = 0; j <= 5; j++)
            {
                array[(i * 6) + 5 - j] = (va & (1 << j)) != 0;
            }
        }
        return array;
    }

    public static int TakeBits(ref Span<bool> bits, int length)
    {
        var result = 0;
        for (var i = 0; i < length; i++)
        {
            if (bits[i])
            {
                result |= 1 << (length - 1 - i);
            }
        }

        bits = bits[length..];
        return result;
    }

    public static async Task<List<(ulong ugcId, string Metadata, DateTime CreatedUtc, ulong OwnerId, int Score)>> GetLeaderboard(int count, int offset)
    {
        var leaderboard = await SteamUserStats.FindLeaderboardAsync("bpb-runs1");
        var scores = await leaderboard.Value.GetScoresAsync(count, offset);
        if (scores is null)
        {
            WriteLine($"    Found no scores between {offset} and {offset + count}");
            return [];
        }
        WriteLine($"    Found {scores.Length} scores of {leaderboard.Value.EntryCount} scores (v: {scores.Min(s => s.Score)} - {scores.Max(s => s.Score)})");
        var fileIds = scores
            .ToDictionary(score => (Steamworks.Data.PublishedFileId) (((ulong) score.Details[0] << 32) + ((uint) score.Details[1])), score => score.Score);
        var pages = await Steamworks.Ugc.Query.All
            .WithMetadata(true)
            .WithFileId(fileIds.Keys.ToArray())
            .GetPageAsync(1);
        WriteLine($"        Query {pages.Value.TotalCount} games");
        var rawGames = pages.Value.Entries.ToList();
        WriteLine($"        Saved {rawGames.Count} games ({new DateTime(rawGames.Min(g => g.Created).Ticks).ToLocalTime()} - {new DateTime(rawGames.Max(g => g.Created).Ticks).ToLocalTime()})");
        return rawGames.Select(g => ((ulong) g.Id, g.Metadata, g.Created.ToUniversalTime(), (ulong) g.Owner.Id, fileIds[g.Id])).ToList();
    }

    public class BackpackBattlesContext : DbContext
    {
        public DbSet<GameRun> Games { get; set; }
        public DbSet<FailedRun> Failed { get; set; }

        private static DbDataSource ds = null;
        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.HasPostgresEnum<Definitions.CharacterClass>();
            builder.HasPostgresEnum<Definitions.ItemId>();
            builder.HasPostgresEnum<Definitions.League>();
            builder.Entity<GameRun>().OwnsMany(r => r.Rounds, turns =>
            {
                turns.ToJson();
                turns.OwnsMany(r => r.Items);
            });
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (ds == null)
            {
                var dataSourceBuilder = new NpgsqlDataSourceBuilder("Host=localhost;Database=backpackbattles;Username=postgres;Password=**************;Include Error Detail=True;CommandTimeout=60");
                dataSourceBuilder.MapEnum<Definitions.CharacterClass>();
                //dataSourceBuilder.MapEnum<Definitions.ItemId>();
                dataSourceBuilder.MapEnum<Definitions.League>();
                ds = dataSourceBuilder.Build();
            }
            optionsBuilder.UseNpgsql(ds);
        }

        public class FailedRun
        {
            public ulong Id { get; set; }
            public int Score { get; set; }
        }
    }

    public static class Definitions
    {
        private class BitsConfig
        {
            public byte ItemIds;
            public byte X;
            public byte Y;
            public byte Direction;
            public byte Gems;

            private BitsConfig(byte itemIds, byte x, byte y, byte direction, byte gems)
            {
                this.ItemIds = itemIds;
                this.X = x;
                this.Y = y;
                this.Direction = direction;
                this.Gems = gems;
            }

            public static BitsConfig Get(string version)
            {
                return version switch
                {
                    "0.9.0" or "0.9.1" or "0.9.2" or "0.9.3" => new BitsConfig(8, 4, 4, 2, 6),
                    _ => throw new Exception($"Unknown version: {version}"),
                };
            }
        }

        [Owned]
        public class Turn
        {
            public short Health { get; set; }
            public short Stamina { get; set; }
            public bool Win { get; set; }
            public List<Item> Items { get; set; }

            public Turn(short health, short stamina, bool win)
            {
                this.Health = health;
                this.Stamina = stamina;
                this.Win = win;
            }

            public override string ToString()
            {
                var pitems = this.Items
                    .GroupBy(i => i.Type)
                    .OrderByDescending(g => g.Count())
                    .ThenByDescending(g => g.Key)
                    .Select(g => $"{GetName(g.Key)}*{g.Count()}");
                return $"{this.Health} / {this.Stamina}: {string.Join(", ", pitems)}";
            }

            public static Turn Parse(string round, string gameVersion, bool win = true)
            {
                var version = BitsConfig.Get(gameVersion);
                var bits = BitsFromString(round);
                var _dbg = string.Concat(bits.Select(i => i ? "1" : "0"));
                var bs = bits.AsSpan();
                var health = TakeBits(ref bs, 10);
                var stamina = TakeBits(ref bs, 10);
                var items = new List<Item>();
                while (bs.Length >= 8)
                {
                    var itemidi = TakeBits(ref bs, version.ItemIds);
                    var itemId = (ItemId) itemidi;
                    var x = TakeBits(ref bs, version.X);
                    var y = TakeBits(ref bs, version.Y);
                    var dir = TakeBits(ref bs, version.Direction);
                    var gemSockets = GemSocket(itemId);
                    ItemId[]? gems = null;
                    if (gemSockets != 0 && TakeBits(ref bs, 1) != 0)
                    {
                        var gl = new List<ItemId>();
                        for (var gemIndex = 0; gemIndex < gemSockets; gemIndex++)
                        {
                            var v = TakeBits(ref bs, version.Gems);
                            var gem = FromGemType(v);
                            if (gem != null)
                            {
                                gl.Add(gem.Value);
                            }
                        }
                        gems = [.. gl];
                    }

                    items.Add(new Item
                    {
                        Type = itemId,
                        X = (byte) x,
                        Y = (byte) y,
                        Direction = (byte) dir,
                        GemSockets = gems
                    });
                }

                return new Turn((short) health, (short) stamina, win)
                {
                    Items = [.. items]
                };
            }
        }

        public class Item
        {
            public ItemId Type { get; set; }
            public byte X { get; set; }
            public byte Y { get; set; }
            public byte Direction { get; set; }
            public ItemId[]? GemSockets { get; set; }
        }

        #region Data

        public static string SanitizeName(string name)
        {
            var sp = name.Split('-', ' ');
            for (var i = 0; i < sp.Length; i++)
            {
                sp[i] = char.ToUpper(sp[i][0]) + sp[i][1..];
            }
            return string.Concat(sp);
        }

        private static readonly int[] _leagues = [850, 550, 480, 400, 300, 200, 120, 50, 0, -1000];
        public static double ToRating(League league, int rating = 0)
        {
            if (league != League.Cheater)
            {
                var l = _leagues[(int) league];
                var l1 = _leagues[(int) league - 1];
                return (l1 - l) * rating / 100.0 + l;
            }
            else
            {
                return _leagues[0] + rating;
            }
        }

        public static League ParseLeague(double rating)
        {
            return (League) _leagues.Select((v, i) => (v, i)).First(item => rating >= item.v).i;
        }

        public static int GemSocket(ItemId id)
        {
            return id switch
            {
                ItemId.WoodenSword => 1,
                ItemId.Pan => 1,
                ItemId.Broom => 1,
                ItemId.WoodenBuckler => 1,
                ItemId.Spear => 1,
                ItemId.Dagger => 1,
                ItemId.SpikedShield => 1,
                ItemId.Shovel => 1,
                ItemId.Torch => 1,
                ItemId.HungryBlade => 1,
                ItemId.BowAndArrow => 1,
                ItemId.LeatherBoots => 1,
                ItemId.LeatherHelm => 1,
                ItemId.HeroSword => 1,
                ItemId.MagicStaff => 1,
                ItemId.PoisonDagger => 1,
                ItemId.ClawsOfAttack => 1,
                ItemId.BurningTorch => 1,
                ItemId.BloodyDagger => 1,
                ItemId.CritwoodStaff => 1,
                ItemId.Manathirst => 1,
                ItemId.SpectralDagger => 1,
                ItemId.ThornBow => 1,
                ItemId.LuckyBow => 1,
                ItemId.PoisonBow => 1,
                ItemId.EvilCap => 1,
                ItemId.MagicTorch => 1,
                ItemId.FancyFencingRapier => 1,
                ItemId.Crown => 1,
                ItemId.HeartContainer => 1,
                ItemId.RubyWhelp => 1,
                ItemId.VillainSword => 1,
                ItemId.CursedDagger => 1,
                ItemId.Shortbow => 1,
                ItemId.ThornShortbow => 1,
                ItemId.LuckyShortbow => 1,
                ItemId.PoisonShortbow => 1,
                ItemId.BurningSword => 1,
                ItemId.StaffOfFire => 1,
                ItemId.ForgingHammer => 1,
                ItemId.DragonskinBoots => 1,
                ItemId.FrozenBuckler => 1,
                ItemId.EmeraldWhelp => 1,
                ItemId.SapphireWhelp => 1,
                ItemId.AmethystWhelp => 1,
                ItemId.MoltenDagger => 1,
                ItemId.MoltenSpear => 1,
                ItemId.StoneHelm => 1,
                ItemId.HeartOfDarkness => 1,
                ItemId.Hammer => 2,
                ItemId.Axe => 2,
                ItemId.LeatherArmor => 2,
                ItemId.ThornWhip => 2,
                ItemId.ShieldOfValor => 2,
                ItemId.VampiricArmor => 2,
                ItemId.Eggscalibur => 2,
                ItemId.HeroLongsword => 2,
                ItemId.FalconBlade => 2,
                ItemId.Pandamonium => 2,
                ItemId.Lightsaber => 2,
                ItemId.Bloodthorne => 2,
                ItemId.Darksaber => 2,
                ItemId.RubyChonk => 2,
                ItemId.HolySpear => 2,
                ItemId.MoonShield => 2,
                ItemId.Pumpkin => 2,
                ItemId.DancingDragon => 2,
                ItemId.BurningBlade => 2,
                ItemId.FlameWhip => 2,
                ItemId.ObsidianDragon => 2,
                ItemId.ChainWhip => 2,
                ItemId.IceArmor => 2,
                ItemId.Frostbite => 2,
                ItemId.SunShield => 2,
                ItemId.IceDragon => 2,
                ItemId.StoneArmor => 2,
                ItemId.RibSawBlade => 3,
                ItemId.DeathScythe => 3,
                ItemId.HolyArmor => 3,
                ItemId.Crossblades => 3,
                ItemId.CorruptedArmor => 3,
                ItemId.VampiricScythe => 3,
                ItemId.DragonscaleArmor => 3,
                ItemId.SunArmor => 3,
                ItemId.MoonArmor => 3,
                ItemId.Greatsword => 4,
                ItemId.DoubleAxe => 5,
                ItemId.BustedBlade => 6,
                _ => 0
            };
        }

        public static ItemId? FromGemType(int gem)
        {
            return gem switch
            {
                0 => ItemId.ChippedRuby,
                1 => ItemId.FlawedRuby,
                2 => ItemId.RegularRuby,
                3 => ItemId.FlawlessRuby,
                4 => ItemId.PerfectRuby,
                5 => ItemId.ChippedSapphire,
                6 => ItemId.FlawedSapphire,
                7 => ItemId.RegularSapphire,
                8 => ItemId.FlawlessSapphire,
                9 => ItemId.PerfectSapphire,
                10 => ItemId.ChippedTopaz,
                11 => ItemId.FlawedTopaz,
                12 => ItemId.RegularTopaz,
                13 => ItemId.FlawlessTopaz,
                14 => ItemId.PerfectTopaz,
                15 => ItemId.ChippedEmerald,
                16 => ItemId.FlawedEmerald,
                17 => ItemId.RegularEmerald,
                18 => ItemId.FlawlessEmerald,
                19 => ItemId.PerfectEmerald,
                20 => ItemId.ChippedAmethyst,
                21 => ItemId.FlawedAmethyst,
                22 => ItemId.RegularAmethyst,
                23 => ItemId.FlawlessAmethyst,
                24 => ItemId.PerfectAmethyst,
                25 => ItemId.Skull,
                26 => ItemId.CorruptedCrystal,
                27 => ItemId.BadgerRune,
                28 => ItemId.ElephantRune,
                29 => ItemId.HawkRune,
                30 => ItemId.TigerRune,
                31 => ItemId.LumpOfCoal,
                32 => ItemId.BurningCoal,
                _ => null,
            };
        }

        public static string GetName(ItemId id)
        {
            return id switch
            {
                ItemId.Stone => "石头",
                ItemId.WoodenSword => "木剑",
                ItemId.Pan => "平底锅",
                ItemId.Broom => "扫把",
                ItemId.WoodenBuckler => "木盾",
                ItemId.Banana => "香蕉",
                ItemId.Garlic => "大蒜",
                ItemId.HealingHerbs => "治疗草药",
                ItemId.WalrusTusk => "海象尖牙",
                ItemId.Piggybank => "存钱罐",
                ItemId.Whetstone => "磨刀石",
                ItemId.PocketSand => "一口袋沙子",
                ItemId.LumpOfCoal => "一坨煤炭",
                ItemId.LeatherBag => "皮包",
                ItemId.Hammer => "锤子",
                ItemId.Spear => "长矛",
                ItemId.Axe => "战斧",
                ItemId.Dagger => "匕首",
                ItemId.SpikedShield => "尖刺盾牌",
                ItemId.LeatherArmor => "皮制护甲",
                ItemId.GlovesOfHaste => "迅捷手套",
                ItemId.HealthPotion => "生命魔药",
                ItemId.FlyAgaric => "致幻蘑菇",
                ItemId.Blueberries => "蓝莓",
                ItemId.Goobert => "黏黏",
                ItemId.BagOfStones => "一袋石头",
                ItemId.LuckyClover => "幸运三叶草",
                ItemId.CustomerCard => "会员卡",
                ItemId.FannyPack => "腰包",
                ItemId.Carrot => "萝卜",
                ItemId.Coins => "一堆金币",
                ItemId.Shovel => "铲铲",
                ItemId.DeckOfCards => "牌堆",
                ItemId.BurningCoal => "燃烧煤炭",
                ItemId.Torch => "火把",
                ItemId.TheLovers => "恋人",
                ItemId.AceOfSpades => "黑桃A",
                ItemId.WhiteEyesBlueDragon => "白眼青龙",
                ItemId.TheFool => "愚者",
                ItemId.HoloFireLizard => "大火龙",
                ItemId.DarkestLotus => "黑暗莲花",
                ItemId.ThornWhip => "荆棘之鞭",
                ItemId.HungryBlade => "饥饿之刃",
                ItemId.BowAndArrow => "弓箭",
                ItemId.LeatherBoots => "皮质靴",
                ItemId.LeatherHelm => "坚韧头盔",
                ItemId.PestilenceFlask => "瘟疫烧瓶",
                ItemId.StoneSkinPotion => "硬化魔药",
                ItemId.AcornCollar => "橡子颈圈",
                ItemId.Flute => "笛子",
                ItemId.ManaOrb => "魔法球",
                ItemId.CorruptedCrystal => "腐败水晶",
                ItemId.StaminaSack => "耐力包",
                ItemId.HeroSword => "勇者之剑",
                ItemId.MagicStaff => "魔杖",
                ItemId.StrongHealthPotion => "强力生命魔药",
                ItemId.PoisonDagger => "剧毒匕首",
                ItemId.PoisonGoobert => "剧毒黏黏",
                ItemId.ClawsOfAttack => "攻击之爪",
                ItemId.LuckyPiggy => "幸运存钱罐",
                ItemId.CarrotGoobert => "萝卜头黏黏",
                ItemId.BurningTorch => "燃烧火把",
                ItemId.PlatinCustomerCard => "白金会员卡",
                ItemId.ManaPotion => "魔法魔药",
                ItemId.RibSawBlade => "锯刃",
                ItemId.DeathScythe => "死神镰刀",
                ItemId.HolyArmor => "神圣护甲",
                ItemId.ShieldOfValor => "勇气盾牌",
                ItemId.HeroicPotion => "英勇魔药",
                ItemId.Pineapple => "菠萝",
                ItemId.BloodAmulet => "血色护符",
                ItemId.RubyEgg => "红宝石龙蛋",
                ItemId.JynxTorquilla => "歪脖鸟",
                ItemId.PotionBelt => "魔药腰带",
                ItemId.BloodyDagger => "吸血匕首",
                ItemId.StrongHeroicPotion => "强力英勇魔药",
                ItemId.StrongStoneSkinPotion => "强力硬化魔药",
                ItemId.CritwoodStaff => "暴击魔杖",
                ItemId.VampiricArmor => "吸血护甲",
                ItemId.BloodGoobert => "血之黏黏",
                ItemId.SteelGoobert => "钢之黏黏",
                ItemId.Eggscalibur => "荷包蛋圣锅",
                ItemId.Manathirst => "吸魔剑",
                ItemId.SpectralDagger => "幽灵匕首",
                ItemId.HeroLongsword => "英雄长剑",
                ItemId.FalconBlade => "猎鹰战刃",
                ItemId.Pandamonium => "腐败熊猫之锅",
                ItemId.ThornBow => "尖牙弓",
                ItemId.LuckyBow => "天命弓",
                ItemId.PoisonBow => "颠茄剧毒弓",
                ItemId.EvilCap => "腐败头盔",
                ItemId.MagicTorch => "魔法火把",
                ItemId.Fanfare => "号角",
                ItemId.Lightsaber => "光剑",
                ItemId.BookOfLight => "光之书",
                ItemId.DemonicFlask => "恶魔烧瓶",
                ItemId.ProtectivePurse => "守护钱包",
                ItemId.FancyFencingRapier => "酷炫西洋剑",
                ItemId.DjinnLamp => "阿拉丁神灯",
                ItemId.Greatsword => "笨重巨剑",
                ItemId.Crown => "辉耀王冠",
                ItemId.Wolpertinger => "鹿角翼兔",
                ItemId.HeartContainer => "心之容器",
                ItemId.Bloodthorne => "血棘",
                ItemId.Crossblades => "十字剑",
                ItemId.StaffOfUnhealing => "不愈魔杖",
                ItemId.RubyWhelp => "红宝石幼龙",
                ItemId.VampiricGloves => "吸血手套",
                ItemId.Darksaber => "黑暗剑",
                ItemId.LightGoobert => "光之黏黏",
                ItemId.RainbowGoobert => "彩虹超级至尊黏黏",
                ItemId.RainbowGoobertRanger => "彩虹究极布丁黏黏",
                ItemId.RubyChonk => "红宝石巨龙",
                ItemId.HolySpear => "神圣长矛",
                ItemId.MoonShield => "月光盾",
                ItemId.CorruptedArmor => "腐败护甲",
                ItemId.RangerBag => "露营背包",
                ItemId.StorageCoffin => "储物棺材",
                ItemId.ArtifactStoneCold => "遗迹石：寒冷",
                ItemId.VampiricScythe => "血腥镰刀",
                ItemId.VillainSword => "反派之剑",
                ItemId.Skull => "骷髅蒂姆",
                ItemId.ArtifactStoneHeat => "遗迹石：火焰",
                ItemId.Present => "圣诞礼物",
                ItemId.GingerbreadMan => "姜饼杰瑞",
                ItemId.Pumpkin => "南瓜头",
                ItemId.DancingDragon => "舞龙",
                ItemId.PiercingArrow => "穿甲箭",
                ItemId.YggdrasilLeaf => "世界树树叶",
                ItemId.PoisonIvy => "剧毒常春藤",
                ItemId.MegaClover => "巨大三叶草",
                ItemId.BowlOfTreats => "一碗零食",
                ItemId.CursedHairComb => "吸血鬼梳子",
                ItemId.MrStruggles => "抱抱先生",
                ItemId.CursedDagger => "诅咒匕首",
                ItemId.BoxOfRiches => "宝石盒",
                ItemId.ChippedRuby => "红宝石碎片",
                ItemId.FlawedRuby => "劣质红宝石",
                ItemId.RegularRuby => "普通红宝石",
                ItemId.FlawlessRuby => "优质红宝石",
                ItemId.PerfectRuby => "完美红宝石",
                ItemId.ChippedSapphire => "蓝宝石碎片",
                ItemId.FlawedSapphire => "劣质蓝宝石",
                ItemId.RegularSapphire => "普通蓝宝石",
                ItemId.FlawlessSapphire => "优质蓝宝石",
                ItemId.PerfectSapphire => "完美蓝宝石",
                ItemId.ChippedEmerald => "翡翠碎片",
                ItemId.FlawedEmerald => "劣质翡翠",
                ItemId.RegularEmerald => "普通翡翠",
                ItemId.FlawlessEmerald => "优质翡翠",
                ItemId.PerfectEmerald => "完美翡翠",
                ItemId.ChippedTopaz => "黄宝石碎片",
                ItemId.FlawedTopaz => "劣质黄宝石",
                ItemId.RegularTopaz => "普通黄宝石",
                ItemId.FlawlessTopaz => "优质黄宝石",
                ItemId.PerfectTopaz => "完美黄宝石",
                ItemId.ChippedAmethyst => "紫水晶碎片",
                ItemId.FlawedAmethyst => "劣质紫水晶",
                ItemId.RegularAmethyst => "普通紫水晶",
                ItemId.FlawlessAmethyst => "优质紫水晶",
                ItemId.PerfectAmethyst => "完美紫水晶",
                ItemId.Snake => "蛇蛇",
                ItemId.Cauldron => "大煮锅",
                ItemId.Shortbow => "短弓",
                ItemId.ThornShortbow => "尖牙短弓",
                ItemId.LuckyShortbow => "天命短弓",
                ItemId.PoisonShortbow => "颠茄剧毒短弓",
                ItemId.Rat => "鼠鼠",
                ItemId.Squirrel => "松鼠",
                ItemId.Hedgehog => "刺猬",
                ItemId.StrongPestilenceFlask => "强力瘟疫烧瓶",
                ItemId.StrongDemonicFlask => "强力恶魔烧瓶",
                ItemId.StrongManaPotion => "强力魔法魔药",
                ItemId.VampiricPotion => "吸血魔药",
                ItemId.StrongVampiricPotion => "强力吸血魔药",
                ItemId.DivinePotion => "神圣魔药",
                ItemId.StrongDivinePotion => "强力神圣魔药",
                ItemId.FirePit => "火坑",
                ItemId.Flame => "火焰",
                ItemId.Phoenix => "凤凰",
                ItemId.BurningSword => "火焰短剑",
                ItemId.BurningBlade => "火焰剑",
                ItemId.ChiliPepper => "红辣椒",
                ItemId.DraconicOrb => "龙铭珠",
                ItemId.StaffOfFire => "烈火之杖",
                ItemId.FlameWhip => "烈火之鞭",
                ItemId.ObsidianDragon => "黑晶龙",
                ItemId.FriendlyFire => "快乐小火",
                ItemId.BurningBanner => "燃烧旗帜",
                ItemId.DarkLantern => "黯淡提灯",
                ItemId.FrozenFlame => "永冻之火",
                ItemId.DragonNest => "龙的巢穴",
                ItemId.BerserkerBag => "旅行包",
                ItemId.ForgingHammer => "锻造锤",
                ItemId.SpikedCollar => "尖刺项圈",
                ItemId.DoubleAxe => "双刃斧",
                ItemId.ChainWhip => "锁链鞭",
                ItemId.DragonscaleArmor => "龙鳞护甲",
                ItemId.DragonClaws => "龙爪",
                ItemId.Anvil => "铁砧",
                ItemId.DeerTotem => "鹿木守护",
                ItemId.WolfEmblem => "狼徽章",
                ItemId.ShamanMask => "萨满面具",
                ItemId.BrassKnuckles => "铜指虎",
                ItemId.CouragePuppy => "勇敢狼崽",
                ItemId.WisdomPuppy => "睿智狼崽",
                ItemId.PowerPuppy => "全能狼崽",
                ItemId.BustedBlade => "锻造巨剑",
                ItemId.DragonskinBoots => "龙鳞靴",
                ItemId.SpellScrollFrostbolt => "魔法卷轴：冰霜箭",
                ItemId.BookOfIce => "冰霜之书",
                ItemId.IceArmor => "冰霜护甲",
                ItemId.FrozenBuckler => "冰霜盾牌",
                ItemId.Frostbite => "霜冻之剑",
                ItemId.BadgerRune => "獾符文",
                ItemId.ElephantRune => "大象符文",
                ItemId.HawkRune => "猎鹰符文",
                ItemId.TigerRune => "老虎符文",
                ItemId.EmeraldEgg => "翡翠龙蛋",
                ItemId.EmeraldWhelp => "翡翠幼龙",
                ItemId.SapphireEgg => "蓝宝石龙蛋",
                ItemId.SapphireWhelp => "蓝宝石幼龙",
                ItemId.AmethystEgg => "紫水晶龙蛋",
                ItemId.AmethystWhelp => "紫水晶幼龙",
                ItemId.Cheese => "奶酪",
                ItemId.CheeseGoobert => "奶酪黏黏",
                ItemId.ChiliGoobert => "辣椒黏黏",
                ItemId.MoltenDagger => "炽热匕首",
                ItemId.MoltenSpear => "炽热长矛",
                ItemId.RainbowGoobertPyromancer => "彩虹终极圆圆浓稠黏黏",
                ItemId.RainbowGoobertBerserker => "彩虹杀戮者黏黏",
                ItemId.ArmoredCouragePuppy => "护甲勇敢狼崽",
                ItemId.ArmoredWisdomPuppy => "护甲睿智狼崽",
                ItemId.ArmoredPowerPuppy => "护甲全能狼崽",
                ItemId.SunArmor => "日耀护甲",
                ItemId.SunShield => "日耀盾牌",
                ItemId.MoonArmor => "月光护甲",
                ItemId.IceDragon => "冰霜巨龙",
                ItemId.StoneArmor => "石制护甲",
                ItemId.StoneHelm => "石制头盔",
                ItemId.StoneGloves => "力量手套",
                ItemId.Reverse => "反弹！",
                ItemId.MrsStruggles => "贴贴女士",
                ItemId.MissFortune => "命运小姐",
                ItemId.HeartOfDarkness => "黑暗心之容器",
                ItemId.Joker => "Jimbo",
                ItemId.LeafBadge => "Leaf Badge",
                ItemId.SkullBadge => "Skull Badge",
                ItemId.WolfBadge => "Wolf Badge",
                ItemId.FlameBadge => "Flame Badge",
                ItemId.RainbowBadge => "Rainbow Badge",
                ItemId.StoneBadge => "Stone Badge",
                _ => throw new Exception($"Unknown item id {id}"),
            };
        }

#if JSON
        [JsonConverter(typeof(StringEnumConverter))]
#endif
        public enum CharacterClass
        {
            Ranger,
            Reaper,
            Berserker,
            Pyromancer
        }

#if JSON
        [JsonConverter(typeof(StringEnumConverter))]
#endif
        public enum League
        {
            Cheater,
            Gramdma,
            GrandMaster,
            Master,
            Diamond,
            Platinum,
            Gold,
            Silver,
            Bronze,
            Unranked,
        }

#if JSON
        [JsonConverter(typeof(StringEnumConverter))]
#endif
        public enum ItemId
        {
            Stone = 0,
            WoodenSword = 1,
            Pan = 2,
            Broom = 3,
            WoodenBuckler = 4,
            Banana = 5,
            Garlic = 6,
            HealingHerbs = 7,
            WalrusTusk = 8,
            Piggybank = 9,
            Whetstone = 10,
            PocketSand = 20,
            LumpOfCoal = 191,
            LeatherBag = 11,
            Hammer = 12,
            Spear = 14,
            Axe = 132,
            Dagger = 15,
            SpikedShield = 16,
            LeatherArmor = 17,
            GlovesOfHaste = 18,
            HealthPotion = 19,
            FlyAgaric = 21,
            Blueberries = 22,
            Goobert = 23,
            BagOfStones = 13,
            LuckyClover = 24,
            CustomerCard = 25,
            FannyPack = 26,
            Carrot = 124,
            Coins = 126,
            Shovel = 183,
            DeckOfCards = 75,
            BurningCoal = 207,
            Torch = 208,
            TheLovers = 77,
            AceOfSpades = 76,
            WhiteEyesBlueDragon = 78,
            TheFool = 240,
            HoloFireLizard = 79,
            DarkestLotus = 80,
            ThornWhip = 27,
            HungryBlade = 28,
            BowAndArrow = 31,
            LeatherBoots = 34,
            LeatherHelm = 127,
            PestilenceFlask = 36,
            StoneSkinPotion = 37,
            AcornCollar = 39,
            Flute = 40,
            ManaOrb = 41,
            CorruptedCrystal = 160,
            StaminaSack = 42,
            HeroSword = 29,
            MagicStaff = 32,
            StrongHealthPotion = 35,
            PoisonDagger = 30,
            PoisonGoobert = 38,
            ClawsOfAttack = 118,
            LuckyPiggy = 119,
            CarrotGoobert = 168,
            BurningTorch = 209,
            PlatinCustomerCard = 216,
            ManaPotion = 172,
            RibSawBlade = 43,
            DeathScythe = 44,
            HolyArmor = 58,
            ShieldOfValor = 51,
            HeroicPotion = 59,
            Pineapple = 60,
            BloodAmulet = 48,
            RubyEgg = 62,
            JynxTorquilla = 81,
            PotionBelt = 85,
            BloodyDagger = 45,
            StrongHeroicPotion = 55,
            StrongStoneSkinPotion = 46,
            CritwoodStaff = 33,
            VampiricArmor = 50,
            BloodGoobert = 54,
            SteelGoobert = 52,
            Eggscalibur = 125,
            Manathirst = 69,
            SpectralDagger = 70,
            HeroLongsword = 82,
            FalconBlade = 83,
            Pandamonium = 161,
            ThornBow = 192,
            LuckyBow = 193,
            PoisonBow = 194,
            EvilCap = 206,
            MagicTorch = 215,
            Fanfare = 61,
            Lightsaber = 63,
            BookOfLight = 57,
            DemonicFlask = 65,
            ProtectivePurse = 66,
            FancyFencingRapier = 71,
            DjinnLamp = 73,
            Greatsword = 128,
            Crown = 217,
            Wolpertinger = 231,
            HeartContainer = 232,
            Bloodthorne = 47,
            Crossblades = 84,
            StaffOfUnhealing = 56,
            RubyWhelp = 64,
            VampiricGloves = 49,
            Darksaber = 72,
            LightGoobert = 53,
            RainbowGoobert = 74,
            RainbowGoobertRanger = 169,
            RubyChonk = 111,
            HolySpear = 218,
            MoonShield = 227,
            CorruptedArmor = 229,
            RangerBag = 67,
            StorageCoffin = 68,
            ArtifactStoneCold = 138,
            VampiricScythe = 139,
            VillainSword = 140,
            Skull = 156,
            ArtifactStoneHeat = 210,
            Present = 213,
            GingerbreadMan = 214,
            Pumpkin = 157,
            DancingDragon = 230,
            PiercingArrow = 141,
            YggdrasilLeaf = 142,
            PoisonIvy = 145,
            MegaClover = 159,
            BowlOfTreats = 162,
            CursedHairComb = 143,
            MrStruggles = 144,
            CursedDagger = 146,
            BoxOfRiches = 112,
            ChippedRuby = 86,
            FlawedRuby = 87,
            RegularRuby = 88,
            FlawlessRuby = 89,
            PerfectRuby = 90,
            ChippedSapphire = 91,
            FlawedSapphire = 92,
            RegularSapphire = 93,
            FlawlessSapphire = 94,
            PerfectSapphire = 95,
            ChippedEmerald = 101,
            FlawedEmerald = 102,
            RegularEmerald = 103,
            FlawlessEmerald = 104,
            PerfectEmerald = 105,
            ChippedTopaz = 96,
            FlawedTopaz = 97,
            RegularTopaz = 98,
            FlawlessTopaz = 99,
            PerfectTopaz = 100,
            ChippedAmethyst = 106,
            FlawedAmethyst = 107,
            RegularAmethyst = 108,
            FlawlessAmethyst = 109,
            PerfectAmethyst = 110,
            Snake = 166,
            Cauldron = 167,
            Shortbow = 182,
            ThornShortbow = 233,
            LuckyShortbow = 234,
            PoisonShortbow = 235,
            Rat = 163,
            Squirrel = 164,
            Hedgehog = 165,
            StrongPestilenceFlask = 170,
            StrongDemonicFlask = 171,
            StrongManaPotion = 173,
            VampiricPotion = 174,
            StrongVampiricPotion = 175,
            DivinePotion = 176,
            StrongDivinePotion = 177,
            FirePit = 113,
            Flame = 114,
            Phoenix = 115,
            BurningSword = 228,
            BurningBlade = 116,
            ChiliPepper = 117,
            DraconicOrb = 120,
            StaffOfFire = 121,
            FlameWhip = 122,
            ObsidianDragon = 123,
            FriendlyFire = 154,
            BurningBanner = 155,
            DarkLantern = 158,
            FrozenFlame = 185,
            DragonNest = 195,
            BerserkerBag = 129,
            ForgingHammer = 130,
            SpikedCollar = 131,
            DoubleAxe = 133,
            ChainWhip = 134,
            DragonscaleArmor = 135,
            DragonClaws = 136,
            Anvil = 147,
            DeerTotem = 148,
            WolfEmblem = 137,
            ShamanMask = 186,
            BrassKnuckles = 202,
            CouragePuppy = 149,
            WisdomPuppy = 150,
            PowerPuppy = 151,
            BustedBlade = 152,
            DragonskinBoots = 153,
            SpellScrollFrostbolt = 178,
            BookOfIce = 179,
            IceArmor = 180,
            FrozenBuckler = 181,
            Frostbite = 184,
            BadgerRune = 187,
            ElephantRune = 188,
            HawkRune = 189,
            TigerRune = 190,
            EmeraldEgg = 196,
            EmeraldWhelp = 197,
            SapphireEgg = 198,
            SapphireWhelp = 199,
            AmethystEgg = 200,
            AmethystWhelp = 201,
            Cheese = 203,
            CheeseGoobert = 204,
            ChiliGoobert = 205,
            MoltenDagger = 211,
            MoltenSpear = 212,
            RainbowGoobertPyromancer = 219,
            RainbowGoobertBerserker = 220,
            ArmoredCouragePuppy = 221,
            ArmoredWisdomPuppy = 222,
            ArmoredPowerPuppy = 223,
            SunArmor = 224,
            SunShield = 225,
            MoonArmor = 226,
            IceDragon = 236,
            StoneArmor = 237,
            StoneHelm = 238,
            StoneGloves = 239,
            Reverse = 241,
            MrsStruggles = 242,
            MissFortune = 243,
            HeartOfDarkness = 244,
            Joker = 245,
            LeafBadge = 246,
            SkullBadge = 247,
            WolfBadge = 248,
            FlameBadge = 249,
            RainbowBadge = 250,
            StoneBadge = 251,
            VineweaveBasket = 252,
            RelicCase = 253,
            PortableAltar = 254,
            Toolbox = 255
        }
        #endregion
    }
}