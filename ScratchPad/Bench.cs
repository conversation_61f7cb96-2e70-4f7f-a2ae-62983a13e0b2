﻿#define MONOMOD_REORG
using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Running;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Reflection;
using MonoMod.Utils;

BenchmarkRunner.Run<Bench>();

public class Person
{
    public required string FullName { get; set; }
    public required DateOnly DateOfBirth { get; set; }
    public int Age()
    {
        return DateTime.Now.Year - this.DateOfBirth.Year;
    }

    public int CalculateAge(int year)
    {
        return year - this.DateOfBirth.Year;
    }
}

[MemoryDiagnoser(true)]
public class Bench
{
    private Person _typedPerson;
    private dynamic _dynamicPerson;
    private dynamic _dynamicExpandoPerson;
    private ExpandoObject _expandoPerson;
    private MethodInfo _ageMethod;
    private MethodInfo _calculateAgeMethod;
    private Delegate _ageDelegate;
    private Delegate _calculateAgeDelegate;
    private Func<Person, int> _ageFunc;
    private Func<Person, int, int> _calculateAgeFunc;
#if MONOMOD_REORG
    private FastReflectionHelper.FastInvoker _fastAgeInvoker;
    private FastReflectionHelper.FastInvoker _fastCalculateAgeInvoker;
#else
    private FastReflectionDelegate _fastAgeDelegate;
    private FastReflectionDelegate _fastCalculateAgeDelegate;
#endif

    [GlobalSetup]
    public void Setup()
    {
        this._typedPerson = new Person
        {
            FullName = "John Doe",
            DateOfBirth = new DateOnly(1990, 1, 1)
        };
        this._dynamicPerson = new Person
        {
            FullName = "John Doe",
            DateOfBirth = new DateOnly(1990, 1, 1)
        };
        this._dynamicExpandoPerson = new ExpandoObject();
        this._dynamicExpandoPerson.FullName = "John Doe";
        this._dynamicExpandoPerson.DateOfBirth = new DateOnly(1990, 1, 1);
        this._dynamicExpandoPerson.Age = new Func<int, int>(year => DateTime.Now.Year - year);
        this._dynamicExpandoPerson.CalculateAge = new Func<int, int, int>((year, birthYear) => year - birthYear);
        this._expandoPerson = new ExpandoObject();
        this._expandoPerson.TryAdd("FullName", "John Doe");
        this._expandoPerson.TryAdd("DateOfBirth", new DateOnly(1990, 1, 1));
        this._ageMethod = typeof(Person).GetMethod(nameof(Person.Age))!;
        this._calculateAgeMethod = typeof(Person).GetMethod(nameof(Person.CalculateAge))!;
        this._ageDelegate = Delegate.CreateDelegate(typeof(Func<Person, int>), this._ageMethod);
        this._calculateAgeDelegate = Delegate.CreateDelegate(typeof(Func<Person, int, int>), this._calculateAgeMethod);
        this._ageFunc = (Func<Person, int>) this._ageDelegate;
        this._calculateAgeFunc = (Func<Person, int, int>) this._calculateAgeDelegate;
#if MONOMOD_REORG
        this._fastAgeInvoker = this._ageMethod.GetFastInvoker();
        this._fastCalculateAgeInvoker = this._calculateAgeMethod.GetFastInvoker();
#else
        this._fastAgeDelegate = this._ageMethod.CreateFastDelegate();
        this._fastCalculateAgeDelegate = this._calculateAgeMethod.CreateFastDelegate();
#endif
    }

    [Benchmark]
    public int FullExtTypedAge()
    {
        return this._typedPerson.Age();
    }

    [Benchmark]
    public int FullExtDynamicAge()
    {
        return this._dynamicPerson.Age();
    }

    [Benchmark]
    public int FullExtDynamicExpandoAge()
    {
        return this._dynamicExpandoPerson.Age(this._dynamicExpandoPerson.DateOfBirth.Year);
    }

    [Benchmark]
    public int FullExtGetMethodAge()
    {
        return (int) typeof(Person).GetMethod(nameof(Person.Age))!.Invoke(this._typedPerson, null);
    }

    [Benchmark]
    public int FullExtInvokeAge()
    {
        return (int) this._ageMethod.Invoke(this._typedPerson, null)!;
    }

    [Benchmark]
    public int FullExtDelegateAge()
    {
        return (int) this._ageDelegate.DynamicInvoke(this._typedPerson)!;
    }

    [Benchmark]
    public int FullExtFuncAge()
    {
        return this._ageFunc(this._typedPerson);
    }

    [Benchmark]
    public int FullExtFastDelegateAge()
    {
#if MONOMOD_REORG
        return (int) this._fastAgeInvoker(this._typedPerson);
#else
        return (int) this._fastAgeDelegate(this._typedPerson);
#endif
    }

    [Benchmark]
    public int FullExtTypedCalcAge()
    {
        return this._typedPerson.CalculateAge(DateTime.Now.Year);
    }

    [Benchmark]
    public int FullExtDynamicCalcAge()
    {
        return this._dynamicPerson.CalculateAge(DateTime.Now.Year);
    }

    [Benchmark]
    public int FullExtDynamicExpandoCalcAge()
    {
        return this._dynamicExpandoPerson.CalculateAge(DateTime.Now.Year, this._dynamicExpandoPerson.DateOfBirth.Year);
    }

    [Benchmark]
    public int FullExtGetMethodCalcAge()
    {
        return (int) typeof(Person).GetMethod(nameof(Person.CalculateAge))!.Invoke(this._typedPerson, new object[] { DateTime.Now.Year });
    }

    [Benchmark]
    public int FullExtInvokeCalcAge()
    {
        return (int) this._calculateAgeMethod.Invoke(this._typedPerson, new object[] { DateTime.Now.Year })!;
    }

    [Benchmark]
    public int FullExtDelegateCalcAge()
    {
        return (int) this._calculateAgeDelegate.DynamicInvoke(this._typedPerson, DateTime.Now.Year)!;
    }

    [Benchmark]
    public int FullExtFuncCalcAge()
    {
        return this._calculateAgeFunc(this._typedPerson, DateTime.Now.Year);
    }

    [Benchmark]
    public int FullExtFastDelegateCalcAge()
    {
#if MONOMOD_REORG
        return (int) this._fastCalculateAgeInvoker(this._typedPerson, DateTime.Now.Year);
#else
        return (int) this._fastCalculateAgeDelegate(this._typedPerson, DateTime.Now.Year);
#endif
    }
}