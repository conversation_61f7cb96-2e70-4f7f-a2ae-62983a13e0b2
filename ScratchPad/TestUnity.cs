﻿using AssetsTools.NET;
using AssetsTools.NET.Extra;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System;
using System.Linq;
using System.Diagnostics;

string Sanitize(string str)
{
    return str.Replace("\r", " ").Replace("\n", " ");
}

var langs = new List<(string ShownName, string FileName, int Index)> {
    ("enUS", "enus", 0),
    ("enGB", "enus", 0),
    ("frFR", "frfr", 4),
    ("deDE", "dede", 1),
    ("koKR", "kokr", 7),
    ("esES", "eses", 2),
    ("esMX", "esmx", 3),
    ("ruRU", "ruru", 10),
    ("zhTW", "zhtw", 13),
    ("zhCN", "zhcn", 12),
    ("itIT", "itit", 5),
    ("ptBR", "ptbr", 9),
    ("plPL", "plpl", 8),
    ("ptPT", "ptbr", 9),
    ("jaJP", "jajp", 6),
    ("thTH", "thth", 11),
};

var prepared_locid = new List<(int, int)> {
    (498, 2743956), // 巨龙年
    (688, 2755929), // 凤凰年
    (470, 2823961), // 猎人
    (545, 2835690), // 法师
    (631, 2756988), // 德鲁伊
    (632, 2813886), // 圣骑士
    (633, 2745141), // 战士
    (634, 2752304), // 牧师
    (635, 2754272), // 潜行者
    (636, 2756165), // 萨满
    (637, 2762223), // 术士
    (638, 2765548), // 恶魔猎手
    (722, 0), // 史诗
    (918, 0), // 死亡骑士
};

var prepared_locid2 = new List<(int, int)> {
    (498, 2743956), // 巨龙年
    (688, 2755929), // 凤凰年
    (470, 2869505), // 猎人
    (545, 2835690), // 法师
    (631, 2869501), // 德鲁伊
    (632, 2869496), // 圣骑士
    (633, 2869507), // 战士
    (634, 2869497), // 牧师
    (635, 2869498), // 潜行者
    (636, 2869502), // 萨满
    (637, 2869503), // 术士
    (638, 2869506), // 恶魔猎手
    (722, 0), // 史诗
    (918, 0), // 死亡骑士
};

var removed_id = new List<int> { 465 };

var dict = new Dictionary<int, Dictionary<string, string>>();
//var dict = JsonConvert.DeserializeObject<Dictionary<int, Dictionary<string, string>>>(File.ReadAllText("input.json"));

var am = new AssetsManager();
foreach (var (shownName, fileName, index) in langs)
{
    //var (shownName, fileName, index) = langs.First();
    //var bundle = am.LoadBundleFile($"./dbf_{fileName}.unity3d");
    var bundle =
#if true
        am.LoadBundleFile(@$"C:\Program Files (x86)\Hearthstone\Data\Win\dbf_{fileName}.unity3d");
#else
    am.LoadBundleFile($"C:/Projects/hsdbf/dbf_{fileName}.unity3d");
#endif
    var assets = am.LoadAssetsFileFromBundle(bundle, 0, false);
    // var list = assets.table.GetAssetsOfType((int) AssetClassID.MonoBehaviour).ToList();
    //foreach (var asset in list.Select(info => am.GetTypeInstance(assets, info).GetBaseField()))
    //{
    //    if (asset["m_Name"].GetValue()?.AsString())
    //}
    var mapped = new Dictionary<int, string>();
    var cached_desc = new Dictionary<int, string>();

    AssetTypeValueField REWARD_LIST = null;
    AssetTypeValueField BOOSTER = null;
    var unvisited = dict.Keys.Except(removed_id).ToList();

    {
        foreach (var fileinfo in assets.file.GetAssetsOfType(AssetClassID.MonoBehaviour))
        {
            var inner = am.GetBaseField(assets, fileinfo);
            var obj = ToDict(inner["Records"]);
            var js = JsonConvert.SerializeObject(obj);
            if (js.Contains("纳克萨玛斯"))
            {
                Debugger.Break();
            }
        }
    }

    {
        foreach (var fileinfo in assets.file.GetAssetsOfType(AssetClassID.MonoBehaviour))
        {
            var inner = am.GetBaseField(assets, fileinfo);
            if (inner["m_Name"].AsString == "REWARD_LIST")
            {
                REWARD_LIST = inner;
            }
            else if (inner["m_Name"].AsString == "BOOSTER")
            {
                BOOSTER = inner;
            }
        }
    }

    {
        foreach (var pack in REWARD_LIST["Records"][0].Children)
        {
            var locid = pack["m_description"]?["m_locId"]?.AsInt;
            if (locid != null && prepared_locid.Any(i => i.Item2 == locid))
            {
                var locvalue = pack["m_description"]?["m_currentLocaleValue"]?.AsString;
                if (!string.IsNullOrEmpty(locvalue))
                {
                    mapped.Add(locid.Value, Sanitize(locvalue));
                }
            }
        }
    }

    {
        foreach (var pack in BOOSTER["Records"][0].Children)
        {
            var mid = pack["m_ID"]?.AsInt;
            if (mid == null || mid == 0)
            {
                continue;
            }
            {
                var desc = pack["m_noteDesc"]?.AsString;
                if (!string.IsNullOrWhiteSpace(desc) && desc.Contains("Draft"))
                    continue;
                cached_desc.TryAdd(mid.Value, Sanitize(desc!));
            }
            if (!dict.ContainsKey(mid.Value))
            {
                var newdict = new Dictionary<string, string>();
                foreach (var lang in langs)
                {
                    newdict.Add(lang.ShownName, "");
                }
                dict.Add(mid.Value, newdict);
                unvisited.Add(mid.Value);
            }

            var locid = pack["m_name"]?["m_locId"]?.AsInt;
            if (locid != 0)
            {
                var locvalue = pack["m_name"]?["m_currentLocaleValue"]?.AsString;
                if (!string.IsNullOrEmpty(locvalue))
                {
                    unvisited.Remove(mid.Value);
                    locvalue = Sanitize(locvalue);
                    if (string.Equals(locvalue, dict[mid.Value][shownName]))
                    {
                        //Console.WriteLine("We already have " + locvalue);
                    }
                    else
                    {
                        Console.WriteLine($"We change {mid.Value} {dict[mid.Value][shownName]} to {locvalue}");
                        dict[mid.Value][shownName] = locvalue;
                    }
                }
            }
        }
    }

    {
        foreach (var id in unvisited.ToList())
        {
            var textid = 0;
            foreach (var predef in prepared_locid)
            {
                if (predef.Item1 == id)
                {
                    textid = predef.Item2;
                }
            }
            if (mapped.TryGetValue(textid, out var v))
            {
                if (string.Equals(v, dict[id][shownName]))
                {
                    //Console.WriteLine("We already have " + v);
                }
                else
                {
                    Console.WriteLine($"We found fallback REWARD_LIST {dict[id][shownName]} to {v}");
                    dict[id][shownName] = v;
                }
                unvisited.Remove(id);
            }
        }
    }

    if (unvisited.Any())
    {
        Console.WriteLine($"We did not visit (mapped total {mapped.Count}) {string.Join(", ", unvisited)} for {shownName}");
        foreach (var id in unvisited)
        {
            dict[id][shownName] = cached_desc[id];
        }
    }
}
var output = "";
{
    using var ms = new MemoryStream();
    {
        using var sw = new StreamWriter(ms);
        using var jtw = new JsonTextWriter(sw)
        {
            Formatting = Formatting.Indented,
            Indentation = 4,
        };
        new JsonSerializer().Serialize(jtw, dict);
    }
    output = Encoding.Default.GetString(ms.ToArray());
}
File.WriteAllText("packs.json", output);

object ToDict(AssetTypeValueField value)
{
    var result = new Dictionary<string, object>();
    if (value.Children != null)
    {
        foreach (var child in value.Children)
        {
            var innerValue = child.TypeName switch
            {
                "int" => child.AsInt,
                "SInt8" => child.AsSByte,
                "UInt8" => child.AsByte,
                "SInt16" => child.AsShort,
                "UInt16" => child.AsUShort,
                "UInt32" => child.AsUInt,
                "SInt64" => child.AsLong,
                "UInt64" => child.AsULong,
                "double" => child.AsDouble,
                "string" => child.AsString,
                "DbfLocValue" => ToDict(child),
                "Array" => child.Children.Select(ToDict).ToArray()
            };
            result[child.FieldName] = innerValue;
        }
    }
    return result;
}