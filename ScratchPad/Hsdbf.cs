﻿using AssetsTools.NET;
using AssetsTools.NET.Extra;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System;
using System.Xml.Linq;
using Microsoft.Diagnostics.Runtime.Utilities;

static class Program
{
    const string JSON_DIR = @"C:\Projects\PackTracker\PackHistorian\Resources\";
    const string DBF_DIR = @"C:\Program Files (x86)\Hearthstone\Data\Win\";

    static Dictionary<int, int> NameOverride = new Dictionary<int, int> {
        {470, 2869505}, // Hunter pack
        {498, 2743956}, // Year dragon
        {545, 2745142}, // Mage
        {631, 2869501}, // Druid
        {632, 2869496}, // Paladin
        {633, 2869507}, // Warrior
        {634, 2869497}, // Priest
        {635, 2869498}, // Rogue
        {636, 2869502}, // Shaman
        {637, 2869503}, // Warlock
        {638, 2869506}, // Demon Hunter
        {688, 2760137}, // Year phoenix
        // {918, ?}, // Death Knight
    };

    static AssetTypeValueField? GetAsset(this AssetsManager manager, AssetsFileInstance asset, string name)
    {
        foreach (var file in asset.file.AssetInfos)
        {
            var b = manager.GetBaseField(asset, file);
            if (b["m_Name"].AsString == name)
            {
                return b;
            }
        }
        return null;
    }

    static AssetTypeValueField? GetChildren(this AssetTypeValueField field, string key)
    {
        foreach (var element in field.Children)
        {
            if (element.FieldName == key)
            {
                return element;
            }
        }
        return null;
    }

    static object AsJson(this AssetTypeValueField field)
    {
        if (field.Value != null)
        {
            return field.Value.ValueType switch
            {
                AssetValueType.UInt8 => field.AsByte,
                AssetValueType.Int16 => field.AsShort,
                AssetValueType.String => field.AsString,
                AssetValueType.Bool => field.AsBool,
                AssetValueType.Int8 => field.AsSByte,
                AssetValueType.UInt16 => field.AsUShort,
                AssetValueType.Int32 => field.AsInt,
                AssetValueType.UInt32 => field.AsUInt,
                AssetValueType.Int64 => field.AsLong,
                AssetValueType.UInt64 => field.AsULong,
                AssetValueType.Float => field.AsFloat,
                AssetValueType.Double => field.AsDouble,
                AssetValueType.Array => field.Children!.Select(AsJson).ToList(),
                AssetValueType.None or AssetValueType.ManagedReferencesRegistry => null,
                AssetValueType.ByteArray => field.AsByteArray,
                _ => throw new Exception($"{field.TypeName}"),
            };
        }
        else if (field.Children != null)
        {
            var result = new Dictionary<string, object>();
            foreach (var element in field.Children)
            {
                result.Add(element.FieldName, AsJson(element));
            }
            return result;
        }
        throw new Exception();
    }

    static object Value(this object obj, string key)
    {
        if (obj is Dictionary<string, object> dict)
        {
            return dict[key];
        }
        throw new Exception($"object not dict {obj.GetType()}");
    }

    static T Value<T>(this object obj, string key)
    {
        return (T) obj.Value(key);
    }

    static Dictionary<int, Dictionary<Locale, string>> Load()
    {
        Dictionary<Locale, string> ToMapped(object entry)
        {
            var array = entry.Value("m_locValues").Value<List<object>>("Array");
            return LocaleOrders.ToDictionary(kvp => kvp.Key, kvp => array[kvp.Value].ToString().Trim());
        }

        var manager = new AssetsManager()!;
        var bundle = manager.LoadBundleFile(Path.Combine(DBF_DIR, "dbf.unity3d"));
        var asset = manager.LoadAssetsFileFromBundle(bundle, 0);

        {
            foreach (var assetItem in asset.file.AssetInfos)
            {
                var ai1 = manager.GetBaseField(asset, assetItem);
                File.WriteAllText($"dump_{ai1["m_Name"].AsString}.json", JsonConvert.SerializeObject(ai1.AsJson(), Formatting.Indented));
            }
        }

        var booster = manager.GetAsset(asset, "BOOSTER");
        var reward_list = manager.GetAsset(asset, "REWARD_LIST");
        var list = booster!.AsJson().Value("Records").Value<List<object>>("Array");

        var overrides = new Dictionary<int, Dictionary<Locale, string>>();
        foreach (var reward in reward_list!.AsJson().Value("Records").Value<List<object>>("Array"))
        {
            var locId = reward.Value("m_description").Value<int>("m_locId");
            if (NameOverride.ContainsValue(locId))
            {
                overrides.Add(locId, ToMapped(reward.Value("m_description")));
            }
        }

        var values = new Dictionary<int, Dictionary<Locale, string>>();
        foreach (var pack in list)
        {
            var id = pack.Value<int>("m_ID");
            if (string.IsNullOrEmpty(pack.Value<string>("m_packOpeningPrefab")))
            {
                Console.WriteLine($"{id} {pack.Value("m_noteDesc")} skipped");
                continue;
            }
            var newName = NameOverride.TryGetValue(id, out var value)
                ? overrides[value]
                : ToMapped(pack.Value("m_name"));
            values[id] = newName;
        }

        values = values.OrderBy(kvp => kvp.Key).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        return values;
    }

    enum Locale
    {
        enUS = 0,
        enGB = 1,
        frFR = 2,
        deDE = 3,
        koKR = 4,
        esES = 5,
        esMX = 6,
        ruRU = 7,
        zhTW = 8,
        zhCN = 9,
        itIT = 10,
        ptBR = 11,
        plPL = 12,
        ptPT = 13,
        jaJP = 14,
        thTH = 15
    }

    static Dictionary<Locale, int> LocaleOrders = new Dictionary<Locale, int>
    {
        [Locale.enUS] = 0,
        [Locale.enGB] = 0,
        [Locale.frFR] = 4,
        [Locale.deDE] = 1,
        [Locale.koKR] = 7,
        [Locale.esES] = 2,
        [Locale.esMX] = 3,
        [Locale.ruRU] = 10,
        [Locale.zhTW] = 13,
        [Locale.zhCN] = 12,
        [Locale.itIT] = 5,
        [Locale.ptBR] = 9,
        [Locale.plPL] = 8,
        [Locale.ptPT] = 9,
        [Locale.jaJP] = 6,
        [Locale.thTH] = 11,
    };

    static void Main()
    {
        var dbfs = Directory.GetFiles(DBF_DIR).Where(i => i.Contains("dbf_")).ToList();

        var languages = Enum.GetValues<Locale>();
        var values = Load();

        using (var fs = File.OpenRead(Path.Combine(JSON_DIR, "packs.json")))
        {
            using var reader = new StreamReader(fs);
            using var jsonReader = new JsonTextReader(reader);
            var existing = new JsonSerializer().Deserialize<Dictionary<int, Dictionary<Locale, string>>>(jsonReader)!;
            foreach (var kvp in existing)
            {
                if (!values.ContainsKey(kvp.Key))
                {
                    Console.WriteLine($"{kvp.Key} removed");
                    values.Remove(kvp.Key);
                    continue;
                }
                foreach (var map in kvp.Value)
                {
                    if (map.Value != values[kvp.Key][map.Key])
                    {
                        Console.WriteLine($"{map.Value} => {values[kvp.Key][map.Key]} @ {kvp.Key} / {map.Key}");
                    }
                }
            }
        }

        File.WriteAllText(Path.Combine(JSON_DIR, "output.json"), JsonConvert.SerializeObject(values, Formatting.Indented).Replace("  ", "    "));
    }
}